/// Base class for all AutoGen.dart exceptions.
class AutoGenException implements Exception {
  final String message;

  AutoGenException(this.message);

  @override
  String toString() => 'AutoGenException: $message';
}

/// Raised when a handler can't handle a message.
class CantHandleException extends AutoGenException {
  CantHandleException(String message) : super(message);
}

/// Raised when a message can't be delivered.
class UndeliverableException extends AutoGenException {
  UndeliverableException(String message) : super(message);
}

/// Raised when a message is dropped.
class MessageDroppedException extends AutoGenException {
  MessageDroppedException(String message) : super(message);
}

/// Raised when trying to access a value that is not accessible (e.g., a remote resource).
class NotAccessibleError extends AutoGenException {
  NotAccessibleError(String message) : super(message);
}
