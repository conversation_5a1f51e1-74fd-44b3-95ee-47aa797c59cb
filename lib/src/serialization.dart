import 'dart:convert';
import 'dart:typed_data';

import 'package:protobuf/protobuf.dart'; // For Protobuf messages

/// Protocol for message serializers.
abstract class MessageSerializer<T> {
  /// The data content type (e.g., "application/json", "application/x-protobuf").
  String get dataContentType;

  /// The name of the type being serialized (e.g., class name, protobuf full name).
  String get typeName;

  /// Deserializes a payload into a message of type [T].
  T deserialize(Uint8List payload);

  /// Serializes a message of type [T] into a byte payload.
  Uint8List serialize(T message);
}

/// JSON data content type.
const String JSON_DATA_CONTENT_TYPE = 'application/json';

/// Protobuf data content type.
const String PROTOBUF_DATA_CONTENT_TYPE = 'application/x-protobuf';

/// A serializer for JSON-serializable Dart objects (e.g., Freezed classes with JsonSerializable).
class JsonMessageSerializer<T> implements MessageSerializer<T> {
  final T Function(Map<String, dynamic>) _fromJson;
  final Map<String, dynamic> Function(T) _toJson;
  final String _typeName;

  JsonMessageSerializer(this._typeName, this._fromJson, this._toJson);

  @override
  String get dataContentType => JSON_DATA_CONTENT_TYPE;

  @override
  String get typeName => _typeName;

  @override
  T deserialize(Uint8List payload) {
    final messageStr = utf8.decode(payload);
    return _fromJson(jsonDecode(messageStr) as Map<String, dynamic>);
  }

  @override
  Uint8List serialize(T message) {
    return utf8.encode(jsonEncode(_toJson(message)));
  }
}

/// A serializer for Protobuf messages.
class ProtobufMessageSerializer<T extends GeneratedMessage> implements MessageSerializer<T> {
  final T Function() _createEmptyInstance;
  final String _typeName;

  ProtobufMessageSerializer(this._typeName, this._createEmptyInstance);

  @override
  String get dataContentType => PROTOBUF_DATA_CONTENT_TYPE;

  @override
  String get typeName => _typeName;

  @override
  T deserialize(Uint8List payload) {
    final message = _createEmptyInstance();
    message.mergeFromBuffer(payload);
    return message;
  }

  @override
  Uint8List serialize(T message) {
    return message.writeToBuffer();
  }
}

/// Represents a payload that could not be deserialized due to an unknown type or content type.
class UnknownPayload {
  final String typeName;
  final String dataContentType;
  final Uint8List payload;

  UnknownPayload(this.typeName, this.dataContentType, this.payload);
}

/// A registry for managing and retrieving message serializers.
class SerializationRegistry {
  // (type_name, data_content_type) -> serializer
  final Map<(String, String), MessageSerializer<dynamic>> _serializers = {};

  /// Adds a serializer or a list of serializers to the registry.
  void addSerializer(dynamic serializer) {
    if (serializer is List) {
      for (final s in serializer) {
        addSerializer(s);
      }
    } else if (serializer is MessageSerializer) {
      _serializers[(serializer.typeName, serializer.dataContentType)] = serializer;
    }
  }

  /// Deserializes a payload using the specified type name and content type.
  dynamic deserialize(Uint8List payload, {required String typeName, required String dataContentType}) {
    final serializer = _serializers[(typeName, dataContentType)];
    if (serializer == null) {
      return UnknownPayload(typeName, dataContentType, payload);
    }
    return serializer.deserialize(payload);
  }

  /// Serializes a message using the specified type name and content type.
  Uint8List serialize(dynamic message, {required String typeName, required String dataContentType}) {
    final serializer = _serializers[(typeName, dataContentType)];
    if (serializer == null) {
      throw ArgumentError('No serializer registered for type $typeName with content type $dataContentType');
    }
    return serializer.serialize(message);
  }

  /// Checks if a serializer is registered for the given type name and content type.
  bool isRegistered(String typeName, String dataContentType) {
    return _serializers.containsKey((typeName, dataContentType));
  }

  /// Returns the type name for a given message object.
  ///
  /// For Protobuf messages, it returns the full name from the descriptor.
  /// For other objects, it returns the class name.
  String getTypeName(dynamic message) {
    if (message is GeneratedMessage) {
      return message.info_.qualifiedMessageName;
    }
    return message.runtimeType.toString();
  }
}
