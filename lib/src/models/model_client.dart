import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../cancellation_token.dart';
import '../tools/base.dart'; // For Tool and ToolSchema
import 'message.dart'; // For LLMMessage (assuming it's defined here)

part 'generated/model_client.freezed.dart';
part 'generated/model_client.g.dart';

/// Represents the result of a model creation call.
@freezed
class CreateResult with _$CreateResult {
  const factory CreateResult({
    required String id,
    required List<LLMMessage> messages,
    RequestUsage? usage,
  }) = _CreateResult;

  factory CreateResult.fromJson(Map<String, dynamic> json) =>
      _$CreateResultFromJson(json);
}

/// Represents the usage statistics for a request.
@freezed
class RequestUsage with _$RequestUsage {
  const factory RequestUsage({
    required int promptTokens,
    required int completionTokens,
    required int totalTokens,
  }) = _RequestUsage;

  factory RequestUsage.fromJson(Map<String, dynamic> json) =>
      _$RequestUsageFromJson(json);
}

/// Defines common model families.
enum ModelFamily {
  @JsonValue('gpt-41')
  gpt41,
  @JsonValue('gpt-45')
  gpt45,
  @JsonValue('gpt-4o')
  gpt4o,
  @JsonValue('o1')
  o1,
  @JsonValue('o3')
  o3,
  @JsonValue('o4')
  o4,
  @JsonValue('gpt-4')
  gpt4,
  @JsonValue('gpt-35')
  gpt35,
  @JsonValue('r1')
  r1,
  @JsonValue('gemini-1.5-flash')
  gemini15Flash,
  @JsonValue('gemini-1.5-pro')
  gemini15Pro,
  @JsonValue('gemini-2.0-flash')
  gemini20Flash,
  @JsonValue('gemini-2.5-pro')
  gemini25Pro,
  @JsonValue('gemini-2.5-flash')
  gemini25Flash,
  @JsonValue('claude-3-haiku')
  claude3Haiku,
  @JsonValue('claude-3-sonnet')
  claude3Sonnet,
  @JsonValue('claude-3-opus')
  claude3Opus,
  @JsonValue('claude-3-5-haiku')
  claude35Haiku,
  @JsonValue('claude-3-5-sonnet')
  claude35Sonnet,
  @JsonValue('claude-3-7-sonnet')
  claude37Sonnet,
  @JsonValue('claude-4-opus')
  claude4Opus,
  @JsonValue('claude-4-sonnet')
  claude4Sonnet,
  @JsonValue('llama-3.3-8b')
  llama338b,
  @JsonValue('llama-3.3-70b')
  llama3370b,
  @JsonValue('llama-4-scout')
  llama4Scout,
  @JsonValue('llama-4-maverick')
  llama4Maverick,
  @JsonValue('codestral')
  codestral,
  @JsonValue('open-codestral-mamba')
  openCodestralMamba,
  @JsonValue('mistral')
  mistral,
  @JsonValue('ministral')
  ministral,
  @JsonValue('pixtral')
  pixtral,
  @JsonValue('unknown')
  unknown,
}

/// Contains information about a model's properties and capabilities.
@freezed
class ModelInfo with _$ModelInfo {
  const factory ModelInfo({
    /// True if the model supports vision (image input).
    required bool vision,

    /// True if the model supports function calling.
    required bool functionCalling,

    /// True if the model supports JSON output (different from structured output).
    required bool jsonOutput,

    /// The family of the model.
    required ModelFamily family,

    /// True if the model supports structured output.
    required bool structuredOutput,

    /// True if the model supports multiple, non-consecutive system messages.
    bool? multipleSystemMessages,
  }) = _ModelInfo;

  factory ModelInfo.fromJson(Map<String, dynamic> json) =>
      _$ModelInfoFromJson(json);
}

/// Abstract class defining the interface for a chat completion client.
///
/// This client interacts with large language models (LLMs) to generate chat completions.
abstract class ChatCompletionClient {
  /// Creates a single response from the model.
  Future<CreateResult> create(
    List<LLMMessage> messages, {
    List<dynamic>? tools, // Can be Tool or ToolSchema
    dynamic toolChoice, // Can be Tool, 'auto', 'required', 'none'
    bool? jsonOutput, // True for JSON mode, or a Type for structured output
    Map<String, dynamic>? extraCreateArgs,
    CancellationToken? cancellationToken,
  });

  /// Creates a stream of string chunks from the model, ending with a [CreateResult].
  Stream<dynamic> createStream(
    List<LLMMessage> messages, {
    List<dynamic>? tools, // Can be Tool or ToolSchema
    dynamic toolChoice, // Can be Tool, 'auto', 'required', 'none'
    bool? jsonOutput, // True for JSON mode, or a Type for structured output
    Map<String, dynamic>? extraCreateArgs,
    CancellationToken? cancellationToken,
  });

  /// Closes the client and releases any resources.
  Future<void> close();

  /// Returns the actual usage statistics for the last request.
  RequestUsage actualUsage();

  /// Returns the total usage statistics across all requests.
  RequestUsage totalUsage();

  /// Counts the number of tokens in the given messages.
  int countTokens(List<LLMMessage> messages, {List<dynamic>? tools});

  /// Returns the remaining tokens available for a given set of messages.
  int remainingTokens(List<LLMMessage> messages, {List<dynamic>? tools});

  /// Information about the model's capabilities and properties.
  ModelInfo get modelInfo;
}
