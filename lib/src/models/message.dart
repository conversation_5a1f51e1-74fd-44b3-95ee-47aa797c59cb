import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../image.dart'; // For Image
import '../types.dart'; // For FunctionCall

part 'generated/message.freezed.dart';
part 'generated/message.g.dart';

/// Base class for all LLM messages.
@Freezed(unionKey: 'type')
sealed class LLMMessage with _$LLMMessage {
  /// System message contains instructions for the model coming from the developer.
  const factory LLMMessage.system({
    @Default('SystemMessage') String type,
    required String content,
  }) = SystemMessage;

  /// User message contains input from end users, or a catch-all for data provided to the model.
  const factory LLMMessage.user({
    @Default('UserMessage') String type,
    required dynamic content, // Can be String or List<dynamic> (String or Image)
    required String source,
  }) = UserMessage;

  /// Assistant message are sampled from the language model.
  const factory LLMMessage.assistant({
    @Default('AssistantMessage') String type,
    required dynamic content, // Can be String or List<FunctionCall>
    String? thought,
    required String source,
  }) = AssistantMessage;

  /// Function execution result contains the output of a function call.
  const factory LLMMessage.functionExecutionResult({
    @Default('FunctionExecutionResultMessage') String type,
    required List<FunctionExecutionResult> content,
  }) = FunctionExecutionResultMessage;

  factory LLMMessage.fromJson(Map<String, dynamic> json) =>
      _$LLMMessageFromJson(json);
}

/// Represents the result of a single function execution.
@freezed
class FunctionExecutionResult with _$FunctionExecutionResult {
  const factory FunctionExecutionResult({
    required String content,
    required String name,
    required String callId,
    bool? isError,
  }) = _FunctionExecutionResult;

  factory FunctionExecutionResult.fromJson(Map<String, dynamic> json) =>
      _$FunctionExecutionResultFromJson(json);
}

/// Represents the usage statistics for a request.
@freezed
class RequestUsage with _$RequestUsage {
  const factory RequestUsage({
    required int promptTokens,
    required int completionTokens,
    required int totalTokens,
  }) = _RequestUsage;

  factory RequestUsage.fromJson(Map<String, dynamic> json) =>
      _$RequestUsageFromJson(json);
}

/// The reason the model finished generating the completion.
enum FinishReason {
  @JsonValue('stop')
  stop,
  @JsonValue('length')
  length,
  @JsonValue('function_calls')
  functionCalls,
  @JsonValue('content_filter')
  contentFilter,
  @JsonValue('unknown')
  unknown,
}

/// Represents the result of a model completion.
@freezed
class CreateResult with _$CreateResult {
  const factory CreateResult({
    required FinishReason finishReason,
    required dynamic content, // Can be String or List<FunctionCall>
    required RequestUsage usage,
    required bool cached,
    String? thought,
  }) = _CreateResult;

  factory CreateResult.fromJson(Map<String, dynamic> json) =>
      _$CreateResultFromJson(json);
}
