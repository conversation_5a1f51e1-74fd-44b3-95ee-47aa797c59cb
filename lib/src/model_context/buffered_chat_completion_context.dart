import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/message.dart'; // For LLMMessage and FunctionExecutionResultMessage
import 'chat_completion_context.dart';

part 'generated/buffered_chat_completion_context.freezed.dart';
part 'generated/buffered_chat_completion_context.g.dart';

/// Configuration for BufferedChatCompletionContext.
@freezed
class BufferedChatCompletionContextConfig with _$BufferedChatCompletionContextConfig {
  const factory BufferedChatCompletionContextConfig({
    required int bufferSize,
    List<LLMMessage>? initialMessages,
  }) = _BufferedChatCompletionContextConfig;

  factory BufferedChatCompletionContextConfig.fromJson(Map<String, dynamic> json) =>
      _$BufferedChatCompletionContextConfigFromJson(json);
}

/// A buffered chat completion context that keeps a view of the last n messages,
/// where n is the buffer size.
class BufferedChatCompletionContext extends ChatCompletionContext {
  final int _bufferSize;

  /// Creates a [BufferedChatCompletionContext].
  ///
  /// - [bufferSize]: The maximum number of messages to keep in the buffer. Must be greater than 0.
  /// - [initialMessages]: Optional initial messages to populate the buffer.
  BufferedChatCompletionContext({
    required int bufferSize,
    List<LLMMessage>? initialMessages,
  })  : _bufferSize = bufferSize,
        super(initialMessages: initialMessages) {
    if (bufferSize <= 0) {
      throw ArgumentError('bufferSize must be greater than 0.');
    }
  }

  @override
  Future<List<LLMMessage>> getMessages() async {
    // Get at most `_bufferSize` recent messages.
    var messages = _messages.sublist(
        _messages.length > _bufferSize ? _messages.length - _bufferSize : 0);

    // Handle the first message is a function call result message.
    if (messages.isNotEmpty && messages.first is FunctionExecutionResultMessage) {
      messages = messages.sublist(1);
    }
    return messages;
  }
}
