import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/message.dart'; // For LLMMessage

part 'generated/chat_completion_context.freezed.dart';
part 'generated/chat_completion_context.g.dart';

/// Represents the state of a chat completion context.
@freezed
class ChatCompletionContextState with _$ChatCompletionContextState {
  const factory ChatCompletionContextState({
    @Default([]) List<LLMMessage> messages,
  }) = _ChatCompletionContextState;

  factory ChatCompletionContextState.fromJson(Map<String, dynamic> json) =>
      _$ChatCompletionContextStateFromJson(json);
}

/// An abstract base class for defining the interface of a chat completion context.
///
/// A chat completion context lets agents store and retrieve LLM messages.
/// It can be implemented with different recall strategies.
abstract class ChatCompletionContext {
  final List<LLMMessage> _messages;

  ChatCompletionContext({List<LLMMessage>? initialMessages})
      : _messages = initialMessages ?? [];

  /// Adds a message to the context.
  Future<void> addMessage(LLMMessage message) async {
    _messages.add(message);
  }

  /// Retrieves the messages from the context.
  Future<List<LLMMessage>> getMessages();

  /// Clears all messages from the context.
  Future<void> clear() async {
    _messages.clear();
  }

  /// Saves the state of the context.
  Future<Map<String, dynamic>> saveState() async {
    return ChatCompletionContextState(messages: _messages).toJson();
  }

  /// Loads the state into the context.
  Future<void> loadState(Map<String, dynamic> state) async {
    _messages.clear();
    _messages.addAll(ChatCompletionContextState.fromJson(state).messages);
  }
}
