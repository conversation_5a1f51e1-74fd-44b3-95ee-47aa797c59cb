import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/message.dart'; // For LLMMessage, FunctionExecutionResultMessage
import '../models/model_client.dart'; // For ChatCompletionClient
import '../tools/base.dart'; // For ToolSchema
import 'chat_completion_context.dart';

part 'generated/token_limited_chat_completion_context.freezed.dart';
part 'generated/token_limited_chat_completion_context.g.dart';

/// Configuration for TokenLimitedChatCompletionContext.
@freezed
class TokenLimitedChatCompletionContextConfig with _$TokenLimitedChatCompletionContextConfig {
  const factory TokenLimitedChatCompletionContextConfig({
    // In Dart, we directly reference the model client, not its config.
    // This config might be used for metadata or overrides.
    required int? tokenLimit,
    List<ToolSchema>? toolSchema,
    List<LLMMessage>? initialMessages,
  }) = _TokenLimitedChatCompletionContextConfig;

  factory TokenLimitedChatCompletionContextConfig.fromJson(Map<String, dynamic> json) =>
      _$TokenLimitedChatCompletionContextConfigFromJson(json);
}

/// A chat completion context that maintains a view of the context up to a token limit.
class TokenLimitedChatCompletionContext extends ChatCompletionContext {
  final ChatCompletionClient _modelClient;
  final int? _tokenLimit;
  final List<ToolSchema> _toolSchema;

  /// Creates a [TokenLimitedChatCompletionContext].
  ///
  /// - [modelClient]: The model client to use for token counting.
  /// - [tokenLimit]: The maximum number of tokens to keep in the context.
  ///   If null, the context will be limited by the model client's remaining tokens.
  /// - [toolSchema]: A list of tool schemas to use in the context.
  /// - [initialMessages]: Optional initial messages.
  TokenLimitedChatCompletionContext({
    required ChatCompletionClient modelClient,
    int? tokenLimit,
    List<ToolSchema>? toolSchema,
    List<LLMMessage>? initialMessages,
  })  : _modelClient = modelClient,
        _tokenLimit = tokenLimit,
        _toolSchema = toolSchema ?? [],
        super(initialMessages: initialMessages) {
    if (tokenLimit != null && tokenLimit <= 0) {
      throw ArgumentError('tokenLimit must be greater than 0.');
    }
  }

  @override
  Future<List<LLMMessage>> getMessages() async {
    var messages = List<LLMMessage>.from(_messages); // Create a mutable copy

    if (_tokenLimit == null) {
      var remainingTokens = _modelClient.remainingTokens(messages, tools: _toolSchema);
      while (remainingTokens < 0 && messages.isNotEmpty) {
        final middleIndex = messages.length ~/ 2;
        messages.removeAt(middleIndex);
        remainingTokens = _modelClient.remainingTokens(messages, tools: _toolSchema);
      }
    } else {
      var tokenCount = _modelClient.countTokens(messages, tools: _toolSchema);
      while (tokenCount > _tokenLimit! && messages.isNotEmpty) {
        final middleIndex = messages.length ~/ 2;
        messages.removeAt(middleIndex);
        tokenCount = _modelClient.countTokens(messages, tools: _toolSchema);
      }
    }

    if (messages.isNotEmpty && messages.first is FunctionExecutionResultMessage) {
      messages = messages.sublist(1);
    }
    return messages;
  }
}
