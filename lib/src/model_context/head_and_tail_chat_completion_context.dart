import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/message.dart'; // For LLMMessage, AssistantMessage, FunctionExecutionResultMessage, UserMessage
import '../types.dart'; // For FunctionCall
import 'chat_completion_context.dart';

part 'generated/head_and_tail_chat_completion_context.freezed.dart';
part 'generated/head_and_tail_chat_completion_context.g.dart';

/// Configuration for HeadAndTailChatCompletionContext.
@freezed
class HeadAndTailChatCompletionContextConfig with _$HeadAndTailChatCompletionContextConfig {
  const factory HeadAndTailChatCompletionContextConfig({
    required int headSize,
    required int tailSize,
    List<LLMMessage>? initialMessages,
  }) = _HeadAndTailChatCompletionContextConfig;

  factory HeadAndTailChatCompletionContextConfig.fromJson(Map<String, dynamic> json) =>
      _$HeadAndAndTailChatCompletionContextConfigFromJson(json);
}

/// A chat completion context that keeps a view of the first n and last m messages,
/// where n is the head size and m is the tail size.
class HeadAndTailChatCompletionContext extends ChatCompletionContext {
  final int _headSize;
  final int _tailSize;

  /// Creates a [HeadAndTailChatCompletionContext].
  ///
  /// - [headSize]: The number of messages to keep from the beginning of the conversation. Must be greater than 0.
  /// - [tailSize]: The number of messages to keep from the end of the conversation. Must be greater than 0.
  /// - [initialMessages]: Optional initial messages.
  HeadAndTailChatCompletionContext({
    required int headSize,
    required int tailSize,
    List<LLMMessage>? initialMessages,
  })  : _headSize = headSize,
        _tailSize = tailSize,
        super(initialMessages: initialMessages) {
    if (headSize <= 0) {
      throw ArgumentError('headSize must be greater than 0.');
    }
    if (tailSize <= 0) {
      throw ArgumentError('tailSize must be greater than 0.');
    }
  }

  @override
  Future<List<LLMMessage>> getMessages() async {
    var headMessages = _messages.sublist(0, _headSize.clamp(0, _messages.length));
    var tailMessages = _messages.sublist(
        (_messages.length - _tailSize).clamp(0, _messages.length));

    // Handle the last message in head being a function call message.
    if (headMessages.isNotEmpty &&
        headMessages.last is AssistantMessage &&
        (headMessages.last as AssistantMessage).content is List &&
        ((headMessages.last as AssistantMessage).content as List)
            .every((item) => item is FunctionCall)) {
      headMessages = headMessages.sublist(0, headMessages.length - 1);
    }

    // Handle the first message in tail being a function call result message.
    if (tailMessages.isNotEmpty &&
        tailMessages.first is FunctionExecutionResultMessage) {
      tailMessages = tailMessages.sublist(1);
    }

    final numSkipped = _messages.length - headMessages.length - tailMessages.length;

    if (numSkipped <= 0) {
      return _messages;
    }

    final placeholderMessage =
        LLMMessage.user(content: 'Skipped $numSkipped messages.', source: 'System');
    return [...headMessages, placeholderMessage, ...tailMessages];
  }
}
