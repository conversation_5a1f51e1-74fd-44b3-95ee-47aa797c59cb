import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/message.dart'; // For LLMMessage
import 'chat_completion_context.dart';

part 'generated/unbounded_chat_completion_context.freezed.dart';
part 'generated/unbounded_chat_completion_context.g.dart';

/// Configuration for UnboundedChatCompletionContext.
@freezed
class UnboundedChatCompletionContextConfig with _$UnboundedChatCompletionContextConfig {
  const factory UnboundedChatCompletionContextConfig({
    List<LLMMessage>? initialMessages,
  }) = _UnboundedChatCompletionContextConfig;

  factory UnboundedChatCompletionContextConfig.fromJson(Map<String, dynamic> json) =>
      _$UnboundedChatCompletionContextConfigFromJson(json);
}

/// An unbounded chat completion context that keeps a view of all the messages.
class UnboundedChatCompletionContext extends ChatCompletionContext {
  /// Creates an [UnboundedChatCompletionContext].
  ///
  /// - [initialMessages]: Optional initial messages.
  UnboundedChatCompletionContext({
    List<LLMMessage>? initialMessages,
  }) : super(initialMessages: initialMessages);

  @override
  Future<List<LLMMessage>> getMessages() async {
    return _messages;
  }
}
