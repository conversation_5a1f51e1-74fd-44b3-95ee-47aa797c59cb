import 'package:autogen_core/src/agent_type.dart';
import 'package:autogen_core/src/exceptions.dart';
import 'package:autogen_core/src/subscription_instantiation_context.dart';
import 'package:autogen_core/src/type_subscription.dart';

/// The default subscription is designed to be a sensible default for applications
/// that only need global scope for agents.
///
/// This subscription by default uses the "default" topic type and attempts to
/// detect the agent type to use based on the instantiation context.
class DefaultSubscription extends TypeSubscription {
  /// Creates a [DefaultSubscription].
  ///
  /// - [topicType]: The topic type to subscribe to. Defaults to "default".
  /// - [agentType]: The agent type to use for the subscription. If null,
  ///   it will attempt to detect the agent type based on the instantiation context.
  DefaultSubscription({
    String topicType = 'default',
    dynamic agentType, // Can be String or AgentType
  }) : super(
          topicType: topicType,
          agentType: agentType ?? _getAgentTypeFromContext(),
        );

  static dynamic _getAgentTypeFromContext() {
    try {
      return SubscriptionInstantiationContext.agentType;
    } catch (e) {
      throw CantHandleException(
        'If agentType is not specified, DefaultSubscription must be created ' +
        'within the subscription callback in AgentRuntime.register',
      );
    }
  }
}

// In Dart, decorators like Python's `default_subscription` and `type_subscription`
// are typically implemented using annotations and code generation.
// The `RoutedAgent` code generator will be responsible for processing
// these annotations and adding the corresponding subscriptions.
