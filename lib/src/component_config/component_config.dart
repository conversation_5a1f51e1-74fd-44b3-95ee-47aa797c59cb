import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/component_config.freezed.dart';
part 'generated/component_config.g.dart';

/// Defines the logical type of a component.
enum ComponentType {
  @JsonValue('model')
  model,
  @JsonValue('agent')
  agent,
  @JsonValue('tool')
  tool,
  @JsonValue('termination')
  termination,
  @JsonValue('token_provider')
  tokenProvider,
  @JsonValue('workbench')
  workbench,
  @JsonValue('unknown')
  unknown, // For custom or unrecognized types
}

/// Model class for a component. Contains all information required to instantiate a component.
@freezed
class ComponentModel with _$ComponentModel {
  const factory ComponentModel({
    /// Describes how the component can be instantiated.
    /// In Dart, this typically refers to a registered factory name or a class identifier.
    required String provider,

    /// Logical type of the component.
    ComponentType? componentType,

    /// Version of the component specification.
    int? version,

    /// Version of the component.
    int? componentVersion,

    /// Description of the component.
    String? description,

    /// Human readable label for the component.
    String? label,

    /// The configuration data for the component.
    required Map<String, dynamic> config,
  }) = _ComponentModel;

  factory ComponentModel.fromJson(Map<String, dynamic> json) =>
      _$ComponentModelFromJson(json);
}

/// Abstract class for components that can be created from a configuration object.
abstract class ComponentFromConfig<ConfigT> {
  /// Creates a new instance of the component from a configuration object.
  static T fromConfig<T, ConfigT>(ConfigT config) {
    throw UnimplementedError('fromConfig must be implemented by subclasses.');
  }
}

/// Abstract class for components that can be dumped to a configuration object.
abstract class ComponentToConfig<ConfigT> {
  /// Dumps the configuration that would be required to create a new instance
  /// of a component matching the configuration of this instance.
  ConfigT toConfig();

  /// Dumps the component to a model that can be loaded back in.
  ///
  /// In Dart, this will serialize the component's configuration.
  /// Dynamic loading from this model is not directly supported as in Python.
  ComponentModel dumpComponent();
}

/// Base class for all components in the AutoGen.dart framework.
///
/// This class combines the capabilities of being created from a config
/// and being able to dump its config.
abstract class ComponentBase<ConfigT>
    implements ComponentFromConfig<ConfigT>, ComponentToConfig<ConfigT> {
  // In Dart, componentType, componentVersion, etc., would typically be
  // static members or part of the ComponentModel.
  // The Python ClassVar concept doesn't directly translate to instance members here.

  @override
  ComponentModel dumpComponent() {
    // This implementation needs to be provided by concrete subclasses
    // or a mixin that knows how to get the componentType, version, etc.
    throw UnimplementedError('dumpComponent must be implemented by subclasses.');
  }
}

// In Dart, dynamic loading of components from a ComponentModel
// would typically involve a central registry of factory functions.
// Example (simplified):
// typedef ComponentFactory = dynamic Function(Map<String, dynamic> config);
// final Map<String, ComponentFactory> _componentRegistry = {};
//
// void registerComponentFactory(String provider, ComponentFactory factory) {
//   _componentRegistry[provider] = factory;
// }
//
// dynamic loadComponent(ComponentModel model) {
//   final factory = _componentRegistry[model.provider];
//   if (factory == null) {
//     throw ArgumentError('No component factory registered for provider: ${model.provider}');
//   }
//   return factory(model.config);
// }
