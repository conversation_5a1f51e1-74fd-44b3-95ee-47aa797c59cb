import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../cancellation_token.dart';
import '../model_context/chat_completion_context.dart'; // Assuming this will be created later
import '../models/message.dart'; // Assuming this will be created later
import 'base_memory.dart';

part 'generated/list_memory.freezed.dart';
part 'generated/list_memory.g.dart';

/// Configuration for ListMemory component.
@freezed
class ListMemoryConfig with _$ListMemoryConfig {
  const factory ListMemoryConfig({
    String? name,
    @Default([]) List<MemoryContent> memoryContents,
  }) = _ListMemoryConfig;

  factory ListMemoryConfig.fromJson(Map<String, dynamic> json) =>
      _$ListMemoryConfigFromJson(json);
}

/// Simple chronological list-based memory implementation.
///
/// This memory implementation stores contents in a list and retrieves them in
/// chronological order. It has an `updateContext` method that updates model contexts
/// by appending all stored memories.
class ListMemory implements Memory {
  final String _name;
  List<MemoryContent> _contents;

  /// Creates a [ListMemory] instance.
  ///
  /// - [name]: Optional identifier for this memory instance.
  /// - [memoryContents]: Initial list of memory contents.
  ListMemory({
    String? name,
    List<MemoryContent>? memoryContents,
  })  : _name = name ?? 'default_list_memory',
        _contents = memoryContents ?? [];

  /// Get the memory instance identifier.
  String get name => _name;

  /// Get the current memory contents.
  List<MemoryContent> get content => _contents;

  /// Set the memory contents.
  set content(List<MemoryContent> value) => _contents = value;

  @override
  Future<UpdateContextResult> updateContext(
      ChatCompletionContext modelContext) async {
    if (_contents.isEmpty) {
      return UpdateContextResult(memories: MemoryQueryResult(results: []));
    }

    final memoryStrings = [
      for (var i = 0; i < _contents.length; i++) 
        '${i + 1}. ${_contents[i].content.toString()}'
    ];

    if (memoryStrings.isNotEmpty) {
      final memoryContext = 
          'Relevant memory content (in chronological order):\n' +
              memoryStrings.join('\n') +
              '\n';
      await modelContext.addMessage(SystemMessage(content: memoryContext));
    }

    return UpdateContextResult(memories: MemoryQueryResult(results: _contents));
  }

  @override
  Future<MemoryQueryResult> query(
    dynamic query,
    CancellationToken? cancellationToken,
  ) async {
    // Query is ignored in this simple implementation, returns all memories.
    return MemoryQueryResult(results: _contents);
  }

  @override
  Future<void> add(MemoryContent content, CancellationToken? cancellationToken) async {
    _contents.add(content);
  }

  @override
  Future<void> clear() async {
    _contents = [];
  }

  @override
  Future<void> close() async {
    // No resources to clean up for ListMemory.
  }
}
