import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../cancellation_token.dart';
import '../image.dart';
import '../model_context/chat_completion_context.dart'; // Assuming this will be created later

part 'generated/base_memory.freezed.dart';
part 'generated/base_memory.g.dart';

/// Supported MIME types for memory content.
enum MemoryMimeType {
  @JsonValue('text/plain')
  text,
  @JsonValue('application/json')
  json,
  @JsonValue('text/markdown')
  markdown,
  @JsonValue('image/*')
  image,
  @JsonValue('application/octet-stream')
  binary,
}

/// Represents the content of a memory item.
@freezed
class MemoryContent with _$MemoryContent {
  const factory MemoryContent({
    /// The content of the memory item. It can be a string, bytes, map, or [Image].
    required dynamic content, // Can be String, Uint8List, Map<String, dynamic>, Image
    required MemoryMimeType mimeType,
    Map<String, dynamic>? metadata,
  }) = _MemoryContent;

  factory MemoryContent.fromJson(Map<String, dynamic> json) =>
      _$MemoryContentFromJson(json);
}

/// Result of a memory query operation.
@freezed
class MemoryQueryResult with _$MemoryQueryResult {
  const factory MemoryQueryResult({
    required List<MemoryContent> results,
  }) = _MemoryQueryResult;

  factory MemoryQueryResult.fromJson(Map<String, dynamic> json) =>
      _$MemoryQueryResultFromJson(json);
}

/// Result of a memory update context operation.
@freezed
class UpdateContextResult with _$UpdateContextResult {
  const factory UpdateContextResult({
    required MemoryQueryResult memories,
  }) = _UpdateContextResult;

  factory UpdateContextResult.fromJson(Map<String, dynamic> json) =>
      _$UpdateContextResultFromJson(json);
}

/// Abstract class defining the interface for memory implementations.
///
/// A memory is the storage for data that can be used to enrich or modify the model context.
abstract class Memory {
  /// Updates the provided model context using relevant memory content.
  Future<UpdateContextResult> updateContext(ChatCompletionContext modelContext);

  /// Queries the memory store and returns relevant entries.
  Future<MemoryQueryResult> query(
    dynamic query, // Can be String or MemoryContent
    CancellationToken? cancellationToken,
  );

  /// Adds new content to memory.
  Future<void> add(MemoryContent content, CancellationToken? cancellationToken);

  /// Clears all entries from memory.
  Future<void> clear();

  /// Cleans up any resources used by the memory implementation.
  Future<void> close();
}
