import 'package:autogen_core/src/agent_id.dart';

/// A static class that provides context for message handling.
///
/// This class allows access to the [AgentId] of the agent currently handling
/// a message. It's typically used internally by the runtime.
class MessageHandlerContext {
  // Private static variable to hold the current agent ID in context.
  static AgentId? _currentAgentId;

  /// Private constructor to prevent instantiation.
  MessageHandlerContext._();

  /// Runs a function within a specific message handler context.
  ///
  /// The [agentId] is set before the function runs and is cleared afterward.
  /// This is crucial to prevent context from leaking between different
  /// message handling operations.
  static R runInContext<R>(AgentId agentId, R Function() body) {
    _currentAgentId = agentId;
    try {
      return body();
    } finally {
      _currentAgentId = null;
    }
  }

  /// Returns the [AgentId] of the agent currently handling a message.
  ///
  /// Throws a [StateError] if called outside of a message handler context.
  static AgentId get agentId {
    final agentId = _currentAgentId;
    if (agentId == null) {
      throw StateError(
        'agentId must be called within a message handler context. ' +
        'This is likely caused by calling it outside of an agent\'s onMessage method.'
      );
    }
    return agentId;
  }
}
