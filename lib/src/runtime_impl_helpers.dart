import 'dart:async';
import 'dart:collection';

import 'agent.dart';
import 'agent_id.dart';
import 'agent_type.dart';
import 'subscription.dart';
import 'topic.dart';

/// Helper function to get an AgentId, potentially instantiating the agent if not lazy.
Future<AgentId> getImpl({
  required dynamic idOrType, // AgentId, AgentType, or String
  required String key,
  required bool lazy,
  required Future<Agent> Function(AgentId) instanceGetter,
}) async {
  AgentId id;
  if (idOrType is AgentId) {
    id = idOrType;
  } else {
    final typeStr = (idOrType is AgentType) ? idOrType.type : idOrType as String;
    id = AgentId(type: typeStr, key: key);
  }

  if (!lazy) {
    await instanceGetter(id);
  }
  return id;
}

/// Manages subscriptions and determines which agents should receive messages for a given topic.
class SubscriptionManager {
  final List<Subscription> _subscriptions = [];
  final Set<TopicId> _seenTopics = HashSet();
  final Map<TopicId, List<AgentId>> _subscribedRecipients = {};

  /// Returns an unmodifiable list of current subscriptions.
  List<Subscription> get subscriptions => UnmodifiableListView(_subscriptions);

  /// Adds a new subscription.
  ///
  /// Throws a [StateError] if the subscription already exists.
  Future<void> addSubscription(Subscription subscription) async {
    if (_subscriptions.any((sub) => sub == subscription)) {
      throw StateError('Subscription already exists');
    }
    _subscriptions.add(subscription);
    _rebuildSubscriptions(_seenTopics);
  }

  /// Removes a subscription by its ID.
  ///
  /// Throws a [StateError] if the subscription does not exist.
  Future<void> removeSubscription(String id) async {
    final initialLength = _subscriptions.length;
    _subscriptions.removeWhere((sub) => sub.id == id);
    if (_subscriptions.length == initialLength) {
      throw StateError('Subscription with ID $id does not exist');
    }
    _rebuildSubscriptions(_seenTopics);
  }

  /// Gets the list of agents subscribed to a specific topic.
  Future<List<AgentId>> getSubscribedRecipients(TopicId topic) async {
    if (!_seenTopics.contains(topic)) {
      _buildForNewTopic(topic);
    }
    return _subscribedRecipients[topic] ?? [];
  }

  /// Rebuilds the subscription mapping for a given set of topics.
  void _rebuildSubscriptions(Set<TopicId> topics) {
    _subscribedRecipients.clear();
    for (final topic in topics) {
      _buildForNewTopic(topic);
    }
  }

  /// Builds the subscription mapping for a new topic.
  void _buildForNewTopic(TopicId topic) {
    _seenTopics.add(topic);
    for (final subscription in _subscriptions) {
      if (subscription.isMatch(topic)) {
        _subscribedRecipients.putIfAbsent(topic, () => []).add(subscription.mapToAgent(topic));
      }
    }
  }
}
