import 'dart:async';

import 'agent_id.dart';
import 'base_agent.dart';
import 'cancellation_token.dart';
import 'exceptions.dart';
import 'message_context.dart';
import 'topic.dart';

/// Defines the context passed to a closure, allowing it to interact with the runtime.
abstract class ClosureContext {
  AgentId get id;
  Future<dynamic> sendMessage(
    dynamic message,
    AgentId recipient, {
    CancellationToken? cancellationToken,
    String? messageId,
  });
  Future<void> publishMessage(
    dynamic message,
    TopicId topicId, {
    CancellationToken? cancellationToken,
  });
}

/// A policy for handling messages of an unknown type.
enum UnknownTypePolicy { error, warn, ignore }

/// A function that handles a message within a closure agent.
typedef ClosureHandler<T> = Future<dynamic> Function(
  ClosureContext context,
  T message,
  MessageContext messageContext,
);

/// An agent whose behavior is defined by a simple closure (function).
///
/// This allows for creating simple, stateless agents without the boilerplate of a full class.
class ClosureAgent extends BaseAgent implements ClosureContext {
  final ClosureHandler<dynamic> _closure;
  final List<Type> _expectedTypes;
  final UnknownTypePolicy _unknownTypePolicy;

  ClosureAgent({
    required String description,
    required ClosureHandler<dynamic> closure,
    required List<Type> expectedTypes,
    UnknownTypePolicy unknownTypePolicy = UnknownTypePolicy.warn,
  })  : _closure = closure,
        _expectedTypes = expectedTypes,
        _unknownTypePolicy = unknownTypePolicy,
        super(description);

  @override
  Future<dynamic> onMessageImpl(dynamic message, MessageContext ctx) async {
    final messageType = message.runtimeType;
    if (!_expectedTypes.contains(messageType) && !_expectedTypes.contains(dynamic)) {
      switch (_unknownTypePolicy) {
        case UnknownTypePolicy.error:
          throw CantHandleException(
            'Message type $messageType not in target types $_expectedTypes of $id.'
            ' Set unknownTypePolicy to \'warn\' to suppress this exception, or \'ignore\' to suppress this warning.',
          );
        case UnknownTypePolicy.warn:
          print(
            'Warning: Message type $messageType not in target types $_expectedTypes of $id.'
            ' Set unknownTypePolicy to \'error\' to raise an exception, or \'ignore\' to suppress this warning.',
          );
          return null;
        case UnknownTypePolicy.ignore:
          return null;
      }
    }
    return await _closure(this, message, ctx);
  }

  @override
  Future<Map<String, dynamic>> saveState() async => {};

  @override
  Future<void> loadState(Map<String, dynamic> state) async {}
}