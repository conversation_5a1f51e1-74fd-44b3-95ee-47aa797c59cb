import 'dart:convert';
import 'dart:typed_data';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/image.freezed.dart';
part 'generated/image.g.dart';

/// Represents an image, primarily for base64 encoded images.
///
/// This class handles conversion to and from base64 strings, and provides
/// a method to convert to OpenAI's image URL format.
@freezed
class Image with _$Image {
  const factory Image({
    /// The base64 encoded string of the image data.
    required String base64Data,
  }) = _Image;

  /// Private constructor for Freezed.
  const Image._();

  /// Creates an [Image] from a base64 encoded string.
  factory Image.fromBase64(String base64String) {
    // Basic validation: check if it's a valid base64 string
    try {
      base64Decode(base64String);
    } catch (e) {
      throw ArgumentError('Invalid base64 string provided for image data.');
    }
    return Image(base64Data: base64String);
  }

  /// Creates an [Image] from a data URI (e.g., "data:image/png;base64,...").
  factory Image.fromUri(String uri) {
    final parts = uri.split(';base64,');
    if (parts.length != 2 || !parts[0].startsWith('data:image/')) {
      throw ArgumentError('Invalid data URI format. Expected "data:image/<type>;base64,<data>".');
    }
    return Image.fromBase64(parts[1]);
  }

  /// Converts the image to a base64 encoded string.
  String toBase64() => base64Data;

  /// Converts the image to a data URI (e.g., "data:image/png;base64,...").
  String toDataUri() {
    // Attempt to infer MIME type from base64 data (simplified)
    String mimeType = 'image/jpeg'; // Default
    try {
      final bytes = base64Decode(base64Data);
      if (bytes.length >= 4) {
        if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47) {
          mimeType = 'image/png';
        } else if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF) {
          mimeType = 'image/jpeg';
        } else if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46) {
          mimeType = 'image/gif';
        }
        // Add more checks for other formats like WEBP if needed
      }
    } catch (_) {
      // Fallback to default if decoding fails
    }
    return 'data:$mimeType;base64,$base64Data';
  }

  /// Converts the image to OpenAI's image URL format.
  ///
  /// - [detail]: The detail level for the image ('auto', 'low', or 'high').
  Map<String, dynamic> toOpenAIFormat({String detail = 'auto'}) {
    return {
      'type': 'image_url',
      'image_url': {
        'url': toDataUri(),
        'detail': detail,
      },
    };
  }

  /// Factory constructor to create an [Image] from a JSON map.
  factory Image.fromJson(Map<String, dynamic> json) => _$ImageFromJson(json);
}
