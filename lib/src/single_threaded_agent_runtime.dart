import 'dart:async';
import 'dart:collection';

import 'agent.dart';
import 'agent_id.dart';
import 'agent_instantiation.dart';
import 'agent_metadata.dart';
import 'agent_runtime.dart';
import 'agent_type.dart';
import 'cancellation_token.dart';
import 'exceptions.dart';
import 'message_context.dart';
import 'subscription.dart';
import 'topic.dart';
import 'package:uuid/uuid.dart';

// Message Envelope classes
abstract class _MessageEnvelope {}

class _SendMessageEnvelope extends _MessageEnvelope {
  final dynamic message;
  final AgentId recipient;
  final AgentId? sender;
  final Completer<dynamic> completer;
  final CancellationToken cancellationToken;
  final String messageId;

  _SendMessageEnvelope({
    required this.message,
    required this.recipient,
    this.sender,
    required this.completer,
    required this.cancellationToken,
    required this.messageId,
  });
}

class _PublishMessageEnvelope extends _MessageEnvelope {
  final dynamic message;
  final TopicId topicId;
  final AgentId? sender;
  final CancellationToken cancellationToken;
  final String messageId;

  _PublishMessageEnvelope({
    required this.message,
    required this.topicId,
    this.sender,
    required this.cancellationToken,
    required this.messageId,
  });
}

class _ResponseMessageEnvelope extends _MessageEnvelope {
  final dynamic message;
  final Completer<dynamic> completer;
  final AgentId sender;
  final AgentId? recipient;

  _ResponseMessageEnvelope({
    required this.message,
    required this.completer,
    required this.sender,
    this.recipient,
  });
}

class SingleThreadedAgentRuntime implements AgentRuntime {
  final _messageController = StreamController<_MessageEnvelope>();
  StreamSubscription? _messageSubscription;

  final _agentFactories = <String, AgentFactory>{};
  final _instantiatedAgents = <AgentId, Agent>{};
  final _subscriptions = <Subscription>[];
  final _uuid = Uuid();

  bool _isRunning = false;

  void start() {
    if (_isRunning) {
      throw StateError('Runtime is already started');
    }
    _isRunning = true;
    _messageSubscription = _messageController.stream.listen(_processEnvelope);
  }

  Future<void> stop() async {
    if (!_isRunning) {
      throw StateError('Runtime is not started');
    }
    _isRunning = false;
    await _messageSubscription?.cancel();
    _messageController.close();
    for (final agent in _instantiatedAgents.values) {
      await agent.close();
    }
  }

  Future<void> _processEnvelope(_MessageEnvelope envelope) async {
    switch (envelope) {
      case _SendMessageEnvelope():
        await _processSend(envelope);
        break;
      case _PublishMessageEnvelope():
        await _processPublish(envelope);
        break;
      case _ResponseMessageEnvelope():
        await _processResponse(envelope);
        break;
    }
  }

  @override
  Future<dynamic> sendMessage(
    dynamic message,
    AgentId recipient, {
    AgentId? sender,
    CancellationToken? cancellationToken,
    String? messageId,
  }) {
    if (!_isRunning) throw StateError('Runtime is not running');
    final completer = Completer<dynamic>();
    final token = cancellationToken ?? CancellationToken();

    _messageController.add(_SendMessageEnvelope(
      message: message,
      recipient: recipient,
      sender: sender,
      completer: completer,
      cancellationToken: token,
      messageId: messageId ?? _uuid.v4(),
    ));

    token.addCallback(() {
      if (!completer.isCompleted) {
        completer.completeError(TimeoutException('Operation was cancelled'));
      }
    });

    return completer.future;
  }

  @override
  Future<void> publishMessage(
    dynamic message,
    TopicId topicId, {
    AgentId? sender,
    CancellationToken? cancellationToken,
    String? messageId,
  }) async {
    if (!_isRunning) throw StateError('Runtime is not running');
    _messageController.add(_PublishMessageEnvelope(
      message: message,
      topicId: topicId,
      sender: sender,
      cancellationToken: cancellationToken ?? CancellationToken(),
      messageId: messageId ?? _uuid.v4(),
    ));
  }

  Future<void> _processSend(_SendMessageEnvelope envelope) async {
    try {
      final agent = await _getAgent(envelope.recipient);
      final ctx = MessageContext(
        sender: envelope.sender,
        topicId: null,
        isRpc: true,
        cancellationToken: envelope.cancellationToken,
        messageId: envelope.messageId,
      );
      final response = await agent.onMessage(envelope.message, ctx);
      _messageController.add(_ResponseMessageEnvelope(
        message: response,
        completer: envelope.completer,
        sender: envelope.recipient,
        recipient: envelope.sender,
      ));
    } catch (e, s) {
      envelope.completer.completeError(e, s);
    }
  }

  Future<void> _processPublish(_PublishMessageEnvelope envelope) async {
    final matchingSubs = _subscriptions.where((sub) => sub.isMatch(envelope.topicId));
    final recipients = matchingSubs.map((sub) => sub.mapToAgent(envelope.topicId)).toSet();

    for (final recipientId in recipients) {
      if (recipientId == envelope.sender) continue; // Don't send to self

      try {
        final agent = await _getAgent(recipientId);
        final ctx = MessageContext(
          sender: envelope.sender,
          topicId: envelope.topicId,
          isRpc: false,
          cancellationToken: envelope.cancellationToken,
          messageId: envelope.messageId,
        );
        // Fire and forget
        agent.onMessage(envelope.message, ctx).catchError((e) {
          print('Error in publish message handler for $recipientId: $e');
        });
      } catch (e) {
        print('Error getting agent $recipientId for publish: $e');
      }
    }
  }

  Future<void> _processResponse(_ResponseMessageEnvelope envelope) async {
    if (!envelope.completer.isCompleted) {
      envelope.completer.complete(envelope.message);
    }
  }

  Future<Agent> _getAgent(AgentId agentId) async {
    if (_instantiatedAgents.containsKey(agentId)) {
      return _instantiatedAgents[agentId]!;
    }

    final factory = _agentFactories[agentId.type];
    if (factory == null) {
      throw CantHandleException('Agent type ${agentId.type} not found');
    }

    final agent = await AgentInstantiationContext.runInContext(
      (this, agentId),
      () => factory(),
    );
    
    await agent.bindIdAndRuntime(agentId, this);
    _instantiatedAgents[agentId] = agent;
    return agent;
  }

  @override
  Future<AgentType> registerFactory<T extends Agent>(
    AgentType type,
    AgentFactory<T> agentFactory, {
    Type? expectedClass,
  }) async {
    if (_agentFactories.containsKey(type.type)) {
      throw ArgumentError.value(type.type, 'type', 'Agent type already registered');
    }
    _agentFactories[type.type] = agentFactory;
    return type;
  }

  @override
  Future<AgentId> registerAgentInstance(Agent agentInstance, AgentId agentId) async {
     if (_instantiatedAgents.containsKey(agentId)) {
      throw ArgumentError.value(agentId, 'agentId', 'Agent ID already exists');
    }
    await agentInstance.bindIdAndRuntime(agentId, this);
    _instantiatedAgents[agentId] = agentInstance;
    // A dummy factory to prevent errors if a type is used for both instances and factories
    _agentFactories.putIfAbsent(agentId.type, () => () => throw StateError('Instance-only agent type'));
    return agentId;
  }

  @override
  Future<void> addSubscription(Subscription subscription) async {
    _subscriptions.add(subscription);
  }
  
  // Other methods from AgentRuntime interface would be implemented here...
  @override
  Future<Map<String, dynamic>> agentSaveState(AgentId agent) async => (await _getAgent(agent)).saveState();

  @override
  Future<void> agentLoadState(AgentId agent, Map<String, dynamic> state) async => (await _getAgent(agent)).loadState(state);

  @override
  Future<AgentMetadata> agentMetadata(AgentId agent) async => (await _getAgent(agent)).metadata;

  @override
  Future<AgentId> get(AgentId id, {bool lazy = true}) async => id; // Simplified

  @override
  Future<AgentId> getByType(AgentType type, {String key = 'default', bool lazy = true}) async => AgentId(type: type.type, key: key); // Simplified

  @override
  Future<T> getUnderlyingAgentInstance<T extends Agent>(AgentId id) async => await _getAgent(id) as T;

  @override
  Future<void> removeSubscription(String id) async {
    _subscriptions.removeWhere((sub) => sub.id == id);
  }

  @override
  Future<Map<String, dynamic>> saveState() async {
    final state = <String, dynamic>{};
    for (final agent in _instantiatedAgents.values) {
      state[agent.id.toString()] = await agent.saveState();
    }
    return state;
  }

  @override
  Future<void> loadState(Map<String, dynamic> state) async {
    for (final entry in state.entries) {
      final agentId = AgentId.fromStr(entry.key);
      final agent = await _getAgent(agentId);
      await agent.loadState(entry.value);
    }
  }
}
