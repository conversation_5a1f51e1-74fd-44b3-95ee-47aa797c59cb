import 'package:autogen_core/src/agent_type.dart';

/// A static class that provides context for subscription instantiation.
///
/// This class can be used to access the current agent type
/// during subscription creation, such as within a subscription factory function.
class SubscriptionInstantiationContext {
  // Private static variable to hold the current context.
  static AgentType? _currentAgentType;

  /// Private constructor to prevent instantiation.
  SubscriptionInstantiationContext._();

  /// Runs a function within a specific subscription instantiation context.
  ///
  /// The [agentType] is set before the function runs and is cleared afterward.
  /// This is crucial to prevent context from leaking between different
  /// subscription instantiations.
  static R runInContext<R>(AgentType agentType, R Function() body) {
    _currentAgentType = agentType;
    try {
      return body();
    } finally {
      _currentAgentType = null;
    }
  }

  /// Returns the current [AgentType] from the context.
  ///
  /// Throws a [StateError] if called outside of an instantiation context.
  static AgentType get agentType {
    final agentType = _currentAgentType;
    if (agentType == null) {
      throw StateError(
        'agentType must be called within a subscription instantiation context. ' +
        'This is likely caused by creating a subscription outside of the AgentRuntime.register method.'
      );
    }
    return agentType;
  }
}
