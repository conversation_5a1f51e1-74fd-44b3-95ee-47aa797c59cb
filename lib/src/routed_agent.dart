import 'dart:async';

import 'base_agent.dart';
import 'message_context.dart';
import 'logging.dart';

/// An abstract base class for agents that route messages to handlers based on
/// the type of the message and optional matching functions.
///
/// To create a routed agent, subclass this class, add the `@RoutedAgentConfig()`
/// annotation, and add message handlers as methods decorated with either the
/// `@Event` or `@Rpc` decorator. Then, mixin the generated `_$YourAgentName` class.
///
/// The routing logic itself is implemented in a generated mixin.
abstract class RoutedAgent extends BaseAgent {
  RoutedAgent(String description) : super(description);

  @override
  Future<dynamic> onMessageImpl(dynamic message, MessageContext ctx) {
    return routeMessage(message, ctx);
  }

  /// This method is implemented by the code generator in a mixin.
  /// It contains the `switch` statement to route messages to the correct handler.
  Future<dynamic> routeMessage(dynamic message, MessageContext ctx);

  /// Called when a message is received that does not have a matching message handler.
  /// The default implementation logs an info message.
  Future<dynamic> onUnhandledMessage(dynamic message, MessageContext ctx) async {
    autogenLogger.info('Unhandled message: $message from ${ctx.sender}');
    return null;
  }
}
