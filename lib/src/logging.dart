import 'dart:convert';

import 'package:logging/logging.dart';

import 'agent_id.dart';
import 'topic.dart';

/// The logger for AutoGen.dart core events.
final autogenLogger = Logger('AutoGen');

/// Base class for all structured logging events.
abstract class LogEvent {
  Map<String, dynamic> toJson();

  @override
  String toString() => jsonEncode(toJson());
}

/// Event for an LLM call.
class LLMCallEvent implements LogEvent {
  final List<Map<String, dynamic>> messages;
  final Map<String, dynamic> response;
  final int promptTokens;
  final int completionTokens;
  final AgentId? agentId;

  LLMCallEvent({
    required this.messages,
    required this.response,
    required this.promptTokens,
    required this.completionTokens,
    this.agentId,
  });

  @override
  Map<String, dynamic> toJson() => {
        'type': 'LLMCall',
        'messages': messages,
        'response': response,
        'prompt_tokens': promptTokens,
        'completion_tokens': completionTokens,
        'agent_id': agentId?.toString(),
      };
}

/// Event for a tool call.
class ToolCallEvent implements LogEvent {
  final String toolName;
  final Map<String, dynamic> arguments;
  final String result;
  final AgentId? agentId;

  ToolCallEvent({
    required this.toolName,
    required this.arguments,
    required this.result,
    this.agentId,
  });

  @override
  Map<String, dynamic> toJson() => {
        'type': 'ToolCall',
        'tool_name': toolName,
        'arguments': arguments,
        'result': result,
        'agent_id': agentId?.toString(),
      };
}

enum MessageKind { direct, publish, respond }

enum DeliveryStage { send, deliver }

/// Event for a message being sent or delivered.
class MessageEvent implements LogEvent {
  final String payload;
  final AgentId? sender;
  final dynamic receiver; // AgentId or TopicId
  final MessageKind kind;
  final DeliveryStage deliveryStage;

  MessageEvent({
    required this.payload,
    this.sender,
    this.receiver,
    required this.kind,
    required this.deliveryStage,
  });

  @override
  Map<String, dynamic> toJson() => {
        'type': 'Message',
        'payload': payload,
        'sender': sender?.toString(),
        'receiver': receiver?.toString(),
        'kind': kind.name,
        'delivery_stage': deliveryStage.name,
      };
}

// Other event classes like MessageDroppedEvent, MessageHandlerExceptionEvent etc.
// can be added here following the same pattern.
