import 'dart:async';

import 'package:logging/logging.dart';

import '../agent_id.dart';
import '../agent_instantiation.dart';

final _telemetryLogger = Logger('AutoGen.Telemetry');

/// Constants for OpenTelemetry GenAI Semantic Conventions.
/// These are simplified for demonstration purposes.
class GenAiAttributes {
  static const String operationName = 'gen_ai.operation.name';
  static const String system = 'gen_ai.system';
  static const String toolName = 'gen_ai.tool.name';
  static const String toolDescription = 'gen_ai.tool.description';
  static const String toolCallId = 'gen_ai.tool.call_id';
  static const String agentName = 'gen_ai.agent.name';
  static const String agentId = 'gen_ai.agent.id';
  static const String agentDescription = 'gen_ai.agent.description';
  static const String errorType = 'error.type';
}

/// Operation names for GenAI.
enum GenAiOperationName {
  executeTool,
  createAgent,
  invokeAgent,
}

/// A simplified span for tracing operations.
class Span {
  final String name;
  final Map<String, dynamic> attributes;
  final DateTime startTime;
  DateTime? endTime;
  dynamic exception;
  StackTrace? stackTrace;

  Span(this.name, {Map<String, dynamic>? attributes})
      : attributes = attributes ?? {},
        startTime = DateTime.now();

  void recordException(dynamic e, StackTrace? s) {
    exception = e;
    stackTrace = s;
    attributes[GenAiAttributes.errorType] = e.runtimeType.toString();
  }

  void end() {
    endTime = DateTime.now();
    _telemetryLogger.fine('Span ended: $name, duration: ${endTime!.difference(startTime)}');
    if (exception != null) {
      _telemetryLogger.severe('Span $name ended with exception: $exception', exception, stackTrace);
    }
  }
}

/// A simplified tracer.
class Tracer {
  final String name;
  Tracer(this.name);

  /// Starts a new span and executes the body within its context.
  Future<T> startAsCurrentSpan<T>(
    String name, {
    Map<String, dynamic>? attributes,
    required Future<T> Function(Span span) body,
  }) async {
    final span = Span(name, attributes: attributes);
    try {
      return await body(span);
    } catch (e, s) {
      span.recordException(e, s);
      rethrow;
    } finally {
      span.end();
    }
  }
}

/// Global tracer instance (simplified).
final _globalTracer = Tracer('autogen-core');

/// Context manager to create a span for tool execution.
Future<T> traceToolSpan<T>(
  String toolName, {
  String? toolDescription,
  String? toolCallId,
  required Future<T> Function(Span span) body,
}) {
  final attributes = {
    GenAiAttributes.operationName: GenAiOperationName.executeTool.name,
    GenAiAttributes.system: 'autogen',
    GenAiAttributes.toolName: toolName,
  };
  if (toolDescription != null) {
    attributes[GenAiAttributes.toolDescription] = toolDescription;
  }
  if (toolCallId != null) {
    attributes[GenAiAttributes.toolCallId] = toolCallId;
  }

  return _globalTracer.startAsCurrentSpan(
    '${GenAiOperationName.executeTool.name} $toolName',
    attributes: attributes,
    body: body,
  );
}

/// Context manager to create a span for agent creation.
Future<T> traceCreateAgentSpan<T>(
  String agentName, {
  String? agentId,
  String? agentDescription,
  required Future<T> Function(Span span) body,
}) {
  final attributes = {
    GenAiAttributes.operationName: GenAiOperationName.createAgent.name,
    GenAiAttributes.system: 'autogen',
    GenAiAttributes.agentName: agentName,
  };

  // Try to get agent ID from current context if not provided
  final currentAgentId = AgentInstantiationContext.isInFactoryCall
      ? AgentInstantiationContext.currentAgentId.toString()
      : null;
  if (agentId != null) {
    attributes[GenAiAttributes.agentId] = agentId;
  } else if (currentAgentId != null) {
    attributes[GenAiAttributes.agentId] = currentAgentId;
  }

  if (agentDescription != null) {
    attributes[GenAiAttributes.agentDescription] = agentDescription;
  }

  return _globalTracer.startAsCurrentSpan(
    '${GenAiOperationName.createAgent.name} $agentName',
    attributes: attributes,
    body: body,
  );
}

/// Context manager to create a span for invoking an agent.
Future<T> traceInvokeAgentSpan<T>(
  String agentName, {
  String? agentId,
  String? agentDescription,
  required Future<T> Function(Span span) body,
}) {
  final attributes = {
    GenAiAttributes.operationName: GenAiOperationName.invokeAgent.name,
    GenAiAttributes.system: 'autogen',
    GenAiAttributes.agentName: agentName,
  };
  if (agentId != null) {
    attributes[GenAiAttributes.agentId] = agentId;
  }
  if (agentDescription != null) {
    attributes[GenAiAttributes.agentDescription] = agentDescription;
  }

  return _globalTracer.startAsCurrentSpan(
    '${GenAiOperationName.invokeAgent.name} $agentName',
    attributes: attributes,
    body: body,
  );
}
