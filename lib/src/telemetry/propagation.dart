import 'package:freezed_annotation/freezed_annotation.dart';

part 'propagation.freezed.dart';

/// Metadata for an envelope, used for OpenTelemetry context propagation.
@freezed
class EnvelopeMetadata with _$EnvelopeMetadata {
  const factory EnvelopeMetadata({
    String? traceparent,
    String? tracestate,
    // In Dart, OpenTelemetry Link is not directly exposed in a simple way
    // for direct serialization in metadata. We'll omit it for now.
    // List<Link>? links,
  }) = _EnvelopeMetadata;
}

// In a full OpenTelemetry integration, you would use the OpenTelemetry SDK's
// TextMapPropagator to inject and extract trace context.
// For example:
// import 'package:opentelemetry/api.dart' as otel;
//
// EnvelopeMetadata getTelemetryEnvelopeMetadata() {
//   final carrier = <String, String>{};
//   otel.trace.getPropagators().textMapPropagator.inject(carrier, otel.defaultSetter);
//   return EnvelopeMetadata(
//     traceparent: carrier['traceparent'],
//     tracestate: carrier['tracestate'],
//   );
// }
//
// otel.Context getTelemetryContext(dynamic metadata) {
//   if (metadata == null) {
//     return otel.Context.current; // Or a default empty context
//   } else if (metadata is EnvelopeMetadata) {
//     final carrier = <String, String>{};
//     if (metadata.traceparent != null) carrier['traceparent'] = metadata.traceparent!;
//     if (metadata.tracestate != null) carrier['tracestate'] = metadata.tracestate!;
//     return otel.trace.getPropagators().textMapPropagator.extract(carrier, otel.defaultGetter);
//   } else if (metadata is Map<String, String>) {
//     return otel.trace.getPropagators().textMapPropagator.extract(metadata, otel.defaultGetter);
//   } else {
//     throw ArgumentError('Unknown metadata type: ${metadata.runtimeType}');
//   }
// }
