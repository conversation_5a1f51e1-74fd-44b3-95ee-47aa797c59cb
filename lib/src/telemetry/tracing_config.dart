import 'package:logging/logging.dart';

import '../agent_id.dart';
import '../topic.dart';
import 'constants.dart';

final _tracingConfigLogger = Logger('AutoGen.TracingConfig');

/// Represents the kind of an OpenTelemetry Span.
enum SpanKind {
  internal,
  server,
  client,
  producer,
  consumer,
}

/// Abstract class defining the configuration for OpenTelemetry instrumentation.
abstract class TracingConfig<Operation, Destination, ExtraAttributes> {
  /// The name of the module that is being instrumented.
  String get name;

  /// Builds the attributes for the instrumentation configuration.
  Map<String, dynamic> buildAttributes(
    Operation operation,
    Destination destination,
    ExtraAttributes? extraAttributes,
  );

  /// Returns the span name based on the given operation and destination.
  String getSpanName(Operation operation, Destination destination);

  /// Determines the span kind based on the given operation.
  SpanKind getSpanKind(Operation operation);
}

/// Extra attributes for message runtime tracing.
class ExtraMessageRuntimeAttributes {
  final int? messageSize;
  final String? messageType;

  ExtraMessageRuntimeAttributes({this.messageSize, this.messageType});
}

/// Represents the destination of a messaging operation.
typedef MessagingDestination = dynamic; // AgentId, TopicId, String, or null

/// Represents the type of a messaging operation.
enum MessagingOperation {
  create,
  send,
  publish,
  receive,
  intercept,
  process,
  ack,
}

/// Configuration for message runtime instrumentation.
class MessageRuntimeTracingConfig
    implements TracingConfig<MessagingOperation, MessagingDestination, ExtraMessageRuntimeAttributes> {
  final String _runtimeName;

  MessageRuntimeTracingConfig(this._runtimeName);

  @override
  String get name => _runtimeName;

  @override
  Map<String, dynamic> buildAttributes(
    MessagingOperation operation,
    MessagingDestination destination,
    ExtraMessageRuntimeAttributes? extraAttributes,
  ) {
    final attrs = <String, dynamic>{
      'messaging.operation': _getOperationType(operation),
      'messaging.destination': _getDestinationStr(destination),
    };
    if (extraAttributes != null) {
      if (extraAttributes.messageSize != null) {
        attrs['messaging.message.envelope.size'] = extraAttributes.messageSize;
      }
      if (extraAttributes.messageType != null) {
        attrs['messaging.message.type'] = extraAttributes.messageType;
      }
    }
    return attrs;
  }

  @override
  String getSpanName(MessagingOperation operation, MessagingDestination destination) {
    final spanParts = [operation.name];
    final destinationStr = _getDestinationStr(destination);
    if (destinationStr.isNotEmpty) {
      spanParts.add(destinationStr);
    }
    final spanName = spanParts.join(' ');
    return '$telemetryNamespace $spanName';
  }

  @override
  SpanKind getSpanKind(MessagingOperation operation) {
    if ([MessagingOperation.create, MessagingOperation.send, MessagingOperation.publish].contains(operation)) {
      return SpanKind.producer;
    } else if ([MessagingOperation.receive, MessagingOperation.intercept, MessagingOperation.ack].contains(operation)) {
      return SpanKind.consumer;
    } else {
      return SpanKind.client; // Default or for 'process'
    }
  }

  String _getDestinationStr(MessagingDestination destination) {
    if (destination is AgentId) {
      return '${destination.type}.(${destination.key})-A';
    } else if (destination is TopicId) {
      return '${destination.type}.(${destination.source})-T';
    } else if (destination is String) {
      return destination;
    } else if (destination == null) {
      return '';
    } else {
      _tracingConfigLogger.warning('Unknown destination type: ${destination.runtimeType}');
      return '';
    }
  }

  String _getOperationType(MessagingOperation operation) {
    if ([MessagingOperation.send, MessagingOperation.publish].contains(operation)) {
      return 'publish';
    }
    if ([MessagingOperation.create].contains(operation)) {
      return 'create';
    } else if ([MessagingOperation.receive, MessagingOperation.intercept, MessagingOperation.ack].contains(operation)) {
      return 'receive';
    } else if ([MessagingOperation.process].contains(operation)) {
      return 'process';
    } else {
      return 'Unknown';
    }
  }
}
