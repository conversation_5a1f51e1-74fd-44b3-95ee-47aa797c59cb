import 'dart:async';
import 'dart:io'; // For Platform.environment

import 'package:logging/logging.dart';

import 'genai.dart'; // For Span, Tracer, GenAiOperationName, traceToolSpan etc.
import 'propagation.dart'; // For EnvelopeMetadata
import 'tracing_config.dart'; // For TracingConfig, MessagingOperation, MessagingDestination, ExtraMessageRuntimeAttributes

final _tracingLogger = Logger('AutoGen.Tracing');

/// A utility class to assist with tracing operations using OpenTelemetry concepts.
///
/// This class provides a method to create and manage spans for tracing operations,
/// following semantic conventions and supporting nested spans through metadata contexts.
class TraceHelper<Operation, Destination, ExtraAttributes> {
  final TracingConfig<Operation, Destination, ExtraAttributes> _instrumentationBuilderConfig;
  final Tracer _tracer;

  TraceHelper(
    TracingConfig<Operation, Destination, ExtraAttributes> instrumentationBuilderConfig,
  )   : _instrumentationBuilderConfig = instrumentationBuilderConfig,
        _tracer = _createTracer(instrumentationBuilderConfig.name);

  static Tracer _createTracer(String name) {
    final disableRuntimeTracing = Platform.environment['AUTOGEN_DISABLE_RUNTIME_TRACING'] == 'true';
    if (disableRuntimeTracing) {
      // Return a no-op tracer if tracing is disabled
      return _NoOpTracer(name);
    }
    return Tracer(name); // Use our simplified Tracer
  }

  /// Creates and manages a span for a given operation.
  ///
  /// - [operation]: The operation being performed (e.g., 'send', 'publish').
  /// - [destination]: The destination of the operation (e.g., AgentId, TopicId).
  /// - [parent]: Optional telemetry metadata from a parent span for context propagation.
  /// - [extraAttributes]: Additional attributes specific to the operation.
  /// - [kind]: The kind of span (e.g., SpanKind.producer, SpanKind.consumer).
  /// - [attributes]: Additional generic attributes for the span.
  /// - [body]: The asynchronous function to execute within the span's context.
  Future<T> traceBlock<T>(
    Operation operation,
    Destination destination,
    EnvelopeMetadata? parent, {
    ExtraAttributes? extraAttributes,
    SpanKind? kind,
    Map<String, dynamic>? attributes,
    required Future<T> Function(Span span) body,
  }) async {
    final spanName = _instrumentationBuilderConfig.getSpanName(operation, destination);
    final spanKind = kind ?? _instrumentationBuilderConfig.getSpanKind(operation);

    final attributesWithDefaults = <String, dynamic>{};
    if (attributes != null) {
      attributesWithDefaults.addAll(attributes);
    }
    attributesWithDefaults.addAll(
        _instrumentationBuilderConfig.buildAttributes(operation, destination, extraAttributes));

    // In a full OpenTelemetry integration, you would extract context from 'parent'
    // and pass it to startAsCurrentSpan. For this simplified version, we'll just
    // pass the attributes.
    return _tracer.startAsCurrentSpan(
      spanName,
      attributes: attributesWithDefaults,
      body: body,
    );
  }
}

/// A no-op tracer for when tracing is disabled.
class _NoOpTracer implements Tracer {
  @override
  final String name;

  _NoOpTracer(this.name);

  @override
  Future<T> startAsCurrentSpan<T>(
    String name, {
    Map<String, dynamic>? attributes,
    required Future<T> Function(Span span) body,
  }) async {
    // Just execute the body without creating a real span.
    return await body(Span(name, attributes: attributes));
  }
}
