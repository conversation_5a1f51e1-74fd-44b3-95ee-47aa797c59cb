import 'dart:async';

import 'agent.dart';
import 'agent_id.dart';
import 'agent_instantiation.dart';
import 'agent_metadata.dart';
import 'agent_runtime.dart';
import 'cancellation_token.dart';
import 'message_context.dart';
import 'subscription.dart';
import 'topic.dart';

/// An abstract base class that provides a skeletal implementation of the [Agent] interface.
///
/// This class handles the common boilerplate, such as binding to a runtime and
/// forwarding message sending to the runtime, allowing subclasses to focus on
/// implementing the core message handling logic in [onMessageImpl].
abstract class BaseAgent implements Agent {
  late AgentId _id;
  late AgentRuntime _runtime;
  final String description;

  BaseAgent(this.description) {
    if (AgentInstantiationContext.isInFactoryCall) {
      _runtime = AgentInstantiationContext.currentRuntime;
      _id = AgentInstantiationContext.currentAgentId;
    }
  }

  @override
  AgentId get id => _id;

  AgentRuntime get runtime => _runtime;

  @override
  AgentMetadata get metadata => AgentMetadata(
        key: _id.key,
        type: _id.type,
        description: description,
      );

  @override
  Future<void> bindIdAndRuntime(AgentId id, AgentRuntime runtime) async {
    // In Dart, we can't check for late variable initialization easily without try-catch.
    // We assume it's only called once by the runtime.
    _id = id;
    _runtime = runtime;
  }

  @override
  Future<dynamic> onMessage(dynamic message, MessageContext ctx) {
    return onMessageImpl(message, ctx);
  }

  /// Abstract method for subclasses to implement their message handling logic.
  Future<dynamic> onMessageImpl(dynamic message, MessageContext ctx);

  /// Sends a message to a recipient via the runtime.
  Future<dynamic> sendMessage(
    dynamic message,
    AgentId recipient, {
    CancellationToken? cancellationToken,
    String? messageId,
  }) {
    return _runtime.sendMessage(
      message,
      recipient,
      sender: id,
      cancellationToken: cancellationToken ?? CancellationToken(),
      messageId: messageId,
    );
  }

  /// Publishes a message to a topic via the runtime.
  Future<void> publishMessage(
    dynamic message,
    TopicId topicId, {
    CancellationToken? cancellationToken,
  }) {
    return _runtime.publishMessage(
      message,
      topicId,
      sender: id,
      cancellationToken: cancellationToken,
    );
  }

  @override
  Future<Map<String, dynamic>> saveState() async {
    print('Warning: saveState not implemented for this agent.');
    return {};
  }

  @override
  Future<void> loadState(Map<String, dynamic> state) async {
    print('Warning: loadState not implemented for this agent.');
  }

  @override
  Future<void> close() async {
    // No-op by default
  }

  /// Registers this agent instance with the given runtime.
  Future<AgentId> registerInstance(AgentRuntime runtime, AgentId agentId) async {
    await runtime.registerAgentInstance(this, agentId);
    // In Dart, subscription logic will be handled more explicitly,
    // often by the runtime or the agent composition root.
    // The automatic subscription from Python's `register_instance` is omitted
    // in favor of a more declarative approach with code generation for RoutedAgent.
    return agentId;
  }
}
