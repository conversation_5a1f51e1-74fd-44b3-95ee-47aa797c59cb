import 'dart:async';

import 'agent_id.dart';
import 'message_context.dart';

/// A marker class for signaling that a message should be dropped by an intervention handler.
///
/// An intervention handler should return an instance of this class to indicate
/// that the message should not be processed further.
class DropMessage {
  const DropMessage();
}

/// An abstract class defining the interface for an intervention handler.
///
/// An intervention handler can be used to modify, log, or drop messages that
/// are being processed by the [AgentRuntime].
///
/// Note: Returning `null` from any of the intervention handler methods will be
/// treated as "no change". If you intend to drop a message, you should return
/// a [DropMessage] instance explicitly.
abstract class InterventionHandler {
  /// Called when a message is submitted to the [AgentRuntime] using `sendMessage`.
  ///
  /// - [message]: The message being sent.
  /// - [messageContext]: The context of the message.
  /// - [recipient]: The intended recipient of the message.
  ///
  /// Returns the modified message, or a [DropMessage] instance to drop the message.
  FutureOr<dynamic> onSend(
    dynamic message, {
    required MessageContext messageContext,
    required AgentId recipient,
  });

  /// Called when a message is published to the [AgentRuntime] using `publishMessage`.
  ///
  /// - [message]: The message being published.
  /// - [messageContext]: The context of the message.
  ///
  /// Returns the modified message, or a [DropMessage] instance to drop the message.
  FutureOr<dynamic> onPublish(
    dynamic message, {
    required MessageContext messageContext,
  });

  /// Called when a response is received by the [AgentRuntime] from an agent's
  /// message handler returning a value.
  ///
  /// - [message]: The response message.
  /// - [sender]: The agent that sent the response.
  /// - [recipient]: The agent that is receiving the response.
  ///
  /// Returns the modified message, or a [DropMessage] instance to drop the message.
  FutureOr<dynamic> onResponse(
    dynamic message, {
    required AgentId sender,
    AgentId? recipient,
  });
}

/// A simple class that provides a default implementation for all intervention
/// handler methods, simply returning the message unchanged.
///
/// This allows for easy subclassing to override only the desired methods.
class DefaultInterventionHandler implements InterventionHandler {
  @override
  FutureOr<dynamic> onSend(
    dynamic message, {
    required MessageContext messageContext,
    required AgentId recipient,
  }) {
    return message;
  }

  @override
  FutureOr<dynamic> onPublish(
    dynamic message, {
    required MessageContext messageContext,
  }) {
    return message;
  }

  @override
  FutureOr<dynamic> onResponse(
    dynamic message, {
    required AgentId sender,
    AgentId? recipient,
  }) {
    return message;
  }
}
