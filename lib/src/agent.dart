import 'agent_id.dart';
import 'agent_metadata.dart';
import 'agent_runtime.dart';
import 'message_context.dart';

/// An abstract class representing the fundamental contract for an agent.
///
/// All agents within the AutoGen.dart framework must implement this interface.
/// It defines the core properties and methods that the [AgentRuntime] uses to
/// manage and communicate with the agent.
abstract class Agent {
  /// The metadata of the agent, providing descriptive information.
  AgentMetadata get metadata;

  /// The unique identifier for the agent.
  AgentId get id;

  /// Binds the agent to a specific [AgentRuntime] and assigns its unique [AgentId].
  /// This method is called by the runtime during the agent registration process.
  Future<void> bindIdAndRuntime(AgentId id, AgentRuntime runtime);

  /// The primary message handler for the agent.
  ///
  /// This method should only be called by the [AgentRuntime], not directly by other agents.
  /// It is the entry point for all messages directed to this agent.
  ///
  /// - [message]: The incoming message.
  /// - [ctx]: The context of the message, containing metadata like sender and topic.
  ///
  /// Returns a [Future] that completes with a response, which can be `null`.
  Future<dynamic> onMessage(dynamic message, MessageContext ctx);

  /// Saves the internal state of the agent.
  ///
  /// The returned state must be a JSON-serializable map.
  Future<Map<String, dynamic>> saveState();

  /// Loads the agent's state from a previously saved state.
  ///
  /// - [state]: A JSON-serializable map obtained from [saveState].
  Future<void> loadState(Map<String, dynamic> state);

  /// Called when the runtime is shutting down.
  ///
  /// Agents should use this method to release any resources they hold.
  Future<void> close();
}
