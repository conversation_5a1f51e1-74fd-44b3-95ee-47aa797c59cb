import 'dart:async';

import 'agent.dart';
import 'agent_id.dart';
import 'agent_metadata.dart';
import 'agent_type.dart';
import 'cancellation_token.dart';
import 'subscription.dart';
import 'topic.dart';

/// A factory function that creates an agent instance.
typedef AgentFactory<T extends Agent> = FutureOr<T> Function();

/// An abstract class that defines the contract for an agent runtime.
///
/// The runtime is the "operating system" for the agents, responsible for
/// message passing, agent lifecycle management, and state persistence.
abstract class AgentRuntime {
  /// Sends a message to a specific agent and gets a response.
  Future<dynamic> sendMessage(
    dynamic message,
    AgentId recipient, {
    AgentId? sender,
    CancellationToken? cancellationToken,
    String? messageId,
  });

  /// Publishes a message to a topic, which will be delivered to all subscribed agents.
  Future<void> publishMessage(
    dynamic message,
    TopicId topicId, {
    AgentId? sender,
    CancellationToken? cancellationToken,
    String? messageId,
  });

  /// Registers an agent factory with the runtime for a specific type.
  Future<AgentType> registerFactory<T extends Agent>(
    AgentType type,
    AgentFactory<T> agentFactory, {
    Type? expectedClass,
  });

  /// Registers a pre-existing agent instance with the runtime.
  Future<AgentId> registerAgentInstance(Agent agentInstance, AgentId agentId);

  /// Tries to get the underlying agent instance by its ID.
  ///
  /// This is generally discouraged but can be useful in some cases.
  /// Throws [StateError] if the agent is not found or not accessible.
  Future<T> getUnderlyingAgentInstance<T extends Agent>(AgentId id);

  /// Gets an agent ID, either by its full ID or by its type and key.
  Future<AgentId> get(AgentId id, {bool lazy = true});
  Future<AgentId> getByType(AgentType type, {String key = 'default', bool lazy = true});

  /// Saves the state of the entire runtime, including all hosted agents.
  Future<Map<String, dynamic>> saveState();

  /// Loads the state of the entire runtime from a previously saved state.
  Future<void> loadState(Map<String, dynamic> state);

  /// Gets the metadata for a specific agent.
  Future<AgentMetadata> agentMetadata(AgentId agent);

  /// Saves the state of a single agent.
  Future<Map<String, dynamic>> agentSaveState(AgentId agent);

  /// Loads the state of a single agent.
  Future<void> agentLoadState(AgentId agent, Map<String, dynamic> state);

  /// Adds a new subscription to the runtime.
  Future<void> addSubscription(Subscription subscription);

  /// Removes a subscription from the runtime by its ID.
  Future<void> removeSubscription(String id);

  // Message serializer methods will be added in a later step.
  // void addMessageSerializer(MessageSerializer serializer);
}
