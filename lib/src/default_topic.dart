import 'package:autogen_core/src/message_handler_context.dart';
import 'package:autogen_core/src/topic.dart';

/// Provides a sensible default for the topic_id and source fields of a [TopicId].
///
/// If created in the context of a message handler, the source will be set to
/// the agent_id of the message handler; otherwise, it will be set to "default".
class DefaultTopicId extends TopicId {
  /// Creates a [DefaultTopicId].
  ///
  /// - [type]: Topic type to publish message to. Defaults to "default".
  /// - [source]: Topic source to publish message to. If null, the source will
  ///   be set to the agent_id of the message handler if in context, otherwise
  ///   it will be set to "default".
  DefaultTopicId({
    String type = 'default',
    String? source,
  }) : super(
          type: type,
          source: source ?? _getDefaultSource(),
        );

  static String _getDefaultSource() {
    try {
      // Attempt to get the agent ID from the current message handler context.
      return MessageHandlerContext.agentId.key;
    } catch (_) {
      // If not in the context of a message handler, use "default".
      return 'default';
    }
  }
}
