import 'agent_id.dart';
import 'topic.dart';

/// An abstract class that defines the contract for a subscription.
///
/// Subscriptions determine which topics an agent is interested in and how a
/// given topic maps to a specific agent instance.
abstract class Subscription {
  /// A unique identifier for the subscription.
  String get id;

  /// Checks if a given [topicId] matches the subscription's criteria.
  ///
  /// Returns `true` if the topic matches, `false` otherwise.
  bool isMatch(TopicId topicId);

  /// Maps a [topicId] to a specific [AgentId].
  ///
  /// This method should only be called if [isMatch] returns `true` for the
  /// given [topicId].
  ///
  /// Throws an exception if the topic cannot be handled.
  AgentId mapToAgent(TopicId topicId);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Subscription && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// A helper alias for a function that creates a list of subscriptions.
/// This is used for defining subscriptions in a deferred or dynamic manner.
typedef UnboundSubscription = Future<List<Subscription>> Function();
