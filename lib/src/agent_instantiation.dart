import 'agent_id.dart';
import 'agent_runtime.dart';

/// A record to hold the context for agent instantiation.
typedef InstantiationContext = (AgentRuntime, AgentId);

/// A static class that provides context for agent instantiation.
///
/// This class can be used to access the current runtime and agent ID
/// during agent instantiation, such as within a factory function or the agent's
/// class constructor.
///
/// This is a Dart equivalent of Python's contextvars, used to make runtime
/// and agentId available implicitly during the agent creation process.
class AgentInstantiationContext {
  // Private static variable to hold the current context.
  static InstantiationContext? _currentContext;

  /// Private constructor to prevent instantiation.
  AgentInstantiationContext._();

  /// Runs a function within a specific instantiation context.
  ///
  /// The [context] is set before the function runs and is cleared afterward.
  /// This is crucial to prevent context from leaking between different
  /// agent instantiations.
  static R runInContext<R>(InstantiationContext context, R Function() body) {
    _currentContext = context;
    try {
      return body();
    } finally {
      _currentContext = null;
    }
  }

  /// Returns the current [AgentRuntime] from the context.
  ///
  /// Throws a [StateError] if called outside of an instantiation context.
  static AgentRuntime get currentRuntime {
    final context = _currentContext;
    if (context == null) {
      throw StateError(
        'currentRuntime must be called within an instantiation context. ' 
        'This is likely caused by directly instantiating an agent instead of using the AgentRuntime.'
      );
    }
    return context.$1;
  }

  /// Returns the current [AgentId] from the context.
  ///
  /// Throws a [StateError] if called outside of an instantiation context.
  static AgentId get currentAgentId {
    final context = _currentContext;
    if (context == null) {
      throw StateError(
        'currentAgentId must be called within an instantiation context. ' 
        'This is likely caused by directly instantiating an agent instead of using the AgentRuntime.'
      );
    }
    return context.$2;
  }

  /// Checks if currently running within an instantiation context.
  static bool get isInFactoryCall => _currentContext != null;
}
