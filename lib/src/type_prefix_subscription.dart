import 'package:uuid/uuid.dart';

import 'agent_id.dart';
import 'agent_type.dart';
import 'exceptions.dart';
import 'subscription.dart';
import 'topic.dart';

/// A subscription that matches topics based on a prefix of the topic type.
///
/// It maps to agents using the source of the topic as the agent key.
class TypePrefixSubscription implements Subscription {
  final String _id;
  final String _topicTypePrefix;
  final String _agentType;

  /// Creates a [TypePrefixSubscription].
  ///
  /// - [topicTypePrefix]: The topic type prefix to match against.
  /// - [agentType]: The type of agent that will handle this subscription.
  /// - [id]: Optional unique ID for the subscription. A UUID will be generated if not provided.
  TypePrefixSubscription({
    required String topicTypePrefix,
    required dynamic agentType,
    String? id,
  })
      : _topicTypePrefix = topicTypePrefix,
        _agentType = (agentType is AgentType) ? agentType.type : agentType as String,
        _id = id ?? const Uuid().v4();

  @override
  String get id => _id;

  /// The topic type prefix this subscription matches.
  String get topicTypePrefix => _topicTypePrefix;

  /// The agent type associated with this subscription.
  String get agentType => _agentType;

  @override
  bool isMatch(TopicId topicId) {
    return topicId.type.startsWith(_topicTypePrefix);
  }

  @override
  AgentId mapToAgent(TopicId topicId) {
    if (!isMatch(topicId)) {
      throw CantHandleException('TopicId does not match the subscription');
    }
    return AgentId(type: _agentType, key: topicId.source);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TypePrefixSubscription) return false;
    // Compare by ID if both have explicit IDs, otherwise by content
    return (id == other.id) ||
        (_agentType == other._agentType && _topicTypePrefix == other._topicTypePrefix);
  }

  @override
  int get hashCode => Object.hash(_topicTypePrefix, _agentType);
}
