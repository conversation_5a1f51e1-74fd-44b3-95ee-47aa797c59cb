import 'package:freezed_annotation/freezed_annotation.dart';

part 'generated/types.freezed.dart';
part 'generated/types.g.dart';

/// Represents a call to a function, typically part of a tool.
@freezed
class FunctionCall with _$FunctionCall {
  const factory FunctionCall({
    required String id,

    /// JSON-encoded arguments for the function.
    required String arguments,

    /// The name of the function to call.
    required String name,
  }) = _FunctionCall;

  factory FunctionCall.fromJson(Map<String, dynamic> json) =>
      _$FunctionCallFromJson(json);
}

/// Represents a call to a tool, which wraps a function call.
@freezed
class ToolCall with _$ToolCall {
  const factory ToolCall({
    required String id,
    required FunctionCall function,
    @Default('function') String type,
  }) = _ToolCall;

  factory ToolCall.fromJson(Map<String, dynamic> json) => _$ToolCallFromJson(json);
}

/// A message containing one or more tool calls.
@freezed
class ToolCallMessage with _$ToolCallMessage {
  const factory ToolCallMessage({
    required List<ToolCall> toolCalls,
    String? content,
  }) = _ToolCallMessage;

  factory ToolCallMessage.fromJson(Map<String, dynamic> json) =>
      _$ToolCallMessageFromJson(json);
}

/// A message containing the response from a tool call.
@freezed
class ToolResponseMessage with _$ToolResponseMessage {
  const factory ToolResponseMessage({
    required String toolCallId,
    required String content,
    @Default(false) bool isError,
  }) = _ToolResponseMessage;

  factory ToolResponseMessage.fromJson(Map<String, dynamic> json) =>
      _$ToolResponseMessageFromJson(json);
}
