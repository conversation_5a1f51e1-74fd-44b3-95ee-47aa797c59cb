import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'generated/function_utils.freezed.dart';
part 'generated/function_utils.g.dart';

/// Represents the parameters of a function as defined by the OpenAI API.
@freezed
class Parameters with _$Parameters {
  const factory Parameters({
    @Default('object') String type,
    required Map<String, dynamic> properties,
    @Default([]) List<String> required,
  }) = _Parameters;

  factory Parameters.fromJson(Map<String, dynamic> json) =>
      _$ParametersFromJson(json);
}

/// Represents a function as defined by the OpenAI API.
@freezed
class Function with _$Function {
  const factory Function({
    required String description,
    required String name,
    required Parameters parameters,
  }) = _Function;

  factory Function.fromJson(Map<String, dynamic> json) => _$FunctionFromJson(json);
}

/// Represents a tool function as defined by the OpenAI API.
@freezed
class ToolFunction with _$ToolFunction {
  const factory ToolFunction({
    @Default('function') String type,
    required Function function,
  }) = _ToolFunction;

  factory ToolFunction.fromJson(Map<String, dynamic> json) =>
      _$ToolFunctionFromJson(json);
}

// The dynamic introspection functions like get_typed_signature,
// args_base_model_from_signature, get_function_schema from Python's
// _function_utils.py will NOT be directly translated to Dart.
// In Dart, you would explicitly define your argument classes using Freezed/JsonSerializable,
// and their JSON schema would be generated by json_serializable.
