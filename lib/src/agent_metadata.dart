import 'package:freezed_annotation/freezed_annotation.dart';

part 'generated/agent_metadata.freezed.dart';

/// A data class containing metadata about an agent.
@freezed
class AgentMetadata with _$AgentMetadata {
  const factory AgentMetadata({
    /// The type of the agent, linking it to a factory.
    required String type,

    /// The unique key identifying the agent instance.
    required String key,

    /// A human-readable description of the agent's purpose.
    required String description,
  }) = _AgentMetadata;
}
