import 'package:freezed_annotation/freezed_annotation.dart';

import 'agent_id.dart';
import 'cancellation_token.dart';
import 'topic.dart';

part 'generated/message_context.freezed.dart';

/// A data class that holds the context for a message being passed between agents.
@freezed
class MessageContext with _$MessageContext {
  const factory MessageContext({
    /// The ID of the agent that sent the message. Can be null if sent from outside the runtime.
    AgentId? sender,

    /// The topic to which the message was published. Can be null for direct messages.
    TopicId? topicId,

    /// Whether the message is part of a Remote Procedure Call (RPC) expecting a reply.
    required bool isRpc,

    /// The token to signal cancellation of the operation.
    required CancellationToken cancellationToken,

    /// A unique identifier for the message.
    required String messageId,
  }) = _MessageContext;
}
