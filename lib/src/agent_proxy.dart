import 'dart:async';

import 'agent_id.dart';
import 'agent_metadata.dart';
import 'agent_runtime.dart';
import 'cancellation_token.dart';

/// A helper class that allows you to use an [AgentId] in place of its associated [Agent].
///
/// This proxy delegates operations to the [AgentRuntime], providing a convenient
/// way to interact with agents without directly holding their instances.
class AgentProxy {
  final AgentId _agentId;
  final AgentRuntime _runtime;

  /// Creates an [AgentProxy] for a given [agentId] and [runtime].
  AgentProxy(this._agentId, this._runtime);

  /// The target agent's ID for this proxy.
  AgentId get id => _agentId;

  /// Retrieves the metadata of the target agent.
  Future<AgentMetadata> get metadata => _runtime.agentMetadata(_agentId);

  /// Sends a message to the target agent via the runtime.
  Future<dynamic> sendMessage(
    dynamic message, {
    required AgentId sender,
    CancellationToken? cancellationToken,
    String? messageId,
  }) async {
    return await _runtime.sendMessage(
      message,
      _agentId,
      sender: sender,
      cancellationToken: cancellationToken,
      messageId: messageId,
    );
  }

  /// Saves the state of the target agent via the runtime.
  Future<Map<String, dynamic>> saveState() async {
    return await _runtime.agentSaveState(_agentId);
  }

  /// Loads the state of the target agent via the runtime.
  Future<void> loadState(Map<String, dynamic> state) async {
    await _runtime.agentLoadState(_agentId, state);
  }
}
