/// A function that determines if a message should be handled by a specific handler.
typedef MessageHandlerMatcher = bool Function(dynamic message);

/// Base class for handler annotations.
class HandlerAnnotation {
  final MessageHandlerMatcher? match;
  const HandlerAnnotation({this.match});
}

/// An annotation to mark a method as an RPC message handler.
class Rpc extends HandlerAnnotation {
  const Rpc({super.match});
}

/// An annotation to mark a method as an event message handler.
class Event extends HandlerAnnotation {
  const Event({super.match});
}

/// An annotation to mark a class for routed agent code generation.
class RoutedAgentConfig {
  const RoutedAgentConfig();
}
