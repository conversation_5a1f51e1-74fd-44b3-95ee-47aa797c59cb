import 'dart:core';

/// A special type to represent 'Any' in contexts where a specific Dart Type is needed.
/// In Dart, `dynamic` is typically used for 'Any' behavior.
class AnyType {
  const AnyType._(); // Private constructor to prevent instantiation
}

/// A constant instance of [AnyType].
const AnyType anyType = AnyType._();

/// Checks if a given type is the [AnyType].
bool isAnyType(Type t) {
  return t == AnyType;
}

/// Returns a list of constituent types for a given type.
///
/// In Dart, this is primarily useful for `dynamic` or nullable types.
/// For complex union-like behaviors, Dart relies on sealed classes or explicit type checking.
List<Type> getTypes(Type t) {
  if (t == dynamic) {
    return [anyType.runtimeType]; // Represent dynamic as our AnyType
  } else if (t.toString().endsWith('?')) {
    // This is a heuristic for nullable types (e.g., 'String?')
    // It's not robust for all cases but covers common scenarios.
    final nonNullableTypeName = t.toString().substring(0, t.toString().length - 1);
    // Attempt to get the actual Type object for the non-nullable version
    // This is tricky without full runtime reflection.
    // For simplicity, we'll return the string representation as a Type.
    return [Type.fromRuntimeType(nonNullableTypeName), Null];
  } else {
    return [t];
  }
}

// Note: Dart's type system handles unions and optionals differently than Python.
// For example, `String?` directly means `String` or `null`.
// There's no direct equivalent to Python's `Union[A, B]` that can be
// introspected at runtime in the same way without code generation or
// explicit type checking.
