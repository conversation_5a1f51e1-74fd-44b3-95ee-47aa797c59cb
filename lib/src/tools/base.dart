import 'dart:async';
import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:logging/logging.dart';

import '../cancellation_token.dart';
import '../logging.dart'; // For ToolCallEvent

part 'generated/base.freezed.dart';
part 'generated/base.g.dart';

final _toolLogger = Logger('AutoGen.Tool');

/// Represents the schema for tool parameters, typically in JSON Schema format.
@freezed
class ParametersSchema with _$ParametersSchema {
  const factory ParametersSchema({
    required String type,
    required Map<String, dynamic> properties,
    @Default([]) List<String> required,
    @Default(false) bool additionalProperties,
  }) = _ParametersSchema;

  factory ParametersSchema.fromJson(Map<String, dynamic> json) =>
      _$ParametersSchemaFromJson(json);
}

/// Represents the schema for a tool, including its name, description, and parameters.
@freezed
class ToolSchema with _$ToolSchema {
  const factory ToolSchema({
    required String name,
    String? description,
    ParametersSchema? parameters,
    @Default(false) bool strict,
  }) = _ToolSchema;

  factory ToolSchema.fromJson(Map<String, dynamic> json) =>
      _$ToolSchemaFromJson(json);
}

/// Configuration for overriding a tool's name and/or description.
@freezed
class ToolOverride with _$ToolOverride {
  const factory ToolOverride({
    String? name,
    String? description,
  }) = _ToolOverride;

  factory ToolOverride.fromJson(Map<String, dynamic> json) =>
      _$ToolOverrideFromJson(json);
}

/// Abstract base class for all tools.
///
/// Tools are functions or operations that agents can execute.
abstract class Tool<ArgsT, ReturnT> {
  /// The name of the tool.
  String get name;

  /// A description of what the tool does.
  String get description;

  /// The JSON schema for the tool's parameters.
  ToolSchema get schema;

  /// Runs the tool with the provided arguments.
  ///
  /// - [args]: The arguments for the tool, typically a map that can be converted to ArgsT.
  /// - [cancellationToken]: A token to signal cancellation of the operation.
  /// - [callId]: An optional identifier for the tool call, used for tracing.
  Future<ReturnT> runJson(
    Map<String, dynamic> args,
    CancellationToken cancellationToken, {
    String? callId,
  });

  /// Saves the state of the tool as a JSON-serializable map.
  Future<Map<String, dynamic>> saveStateJson();

  /// Loads the state of the tool from a JSON-serializable map.
  Future<void> loadStateJson(Map<String, dynamic> state);

  /// Converts the return value of the tool to a string representation.
  String returnValueAsString(ReturnT value) {
    if (value is Map || value is List) {
      return jsonEncode(value);
    }
    return value.toString();
  }
}

/// Abstract base class for tools that can stream their results.
abstract class StreamTool<ArgsT, StreamT, ReturnT> implements Tool<ArgsT, ReturnT> {
  /// Runs the tool and returns a stream of intermediate results, ending with the final return value.
  Stream<dynamic> runJsonStream(
    Map<String, dynamic> args,
    CancellationToken cancellationToken, {
    String? callId,
  });
}

/// Abstract base class for tools that maintain state.
abstract class ToolWithState<ArgsT, ReturnT, StateT> implements Tool<ArgsT, ReturnT> {
  /// Saves the tool's state.
  StateT saveState();

  /// Loads the tool's state.
  void loadState(StateT state);
}

/// Base implementation for a tool.
abstract class BaseTool<ArgsT, ReturnT> implements Tool<ArgsT, ReturnT> {
  final String _name;
  final String _description;
  final bool _strict;

  BaseTool({
    required String name,
    required String description,
    bool strict = false,
  })  : _name = name,
        _description = description,
        _strict = strict;

  @override
  String get name => _name;

  @override
  String get description => _description;

  @override
  ToolSchema get schema {
    // In Dart, we don't have Pydantic's model_json_schema directly.
    // The schema generation would typically be handled by json_serializable
    // or a custom schema generator based on the ArgsT type.
    // For now, this is a placeholder.
    // A more complete implementation would involve analyzing ArgsT's fields.
    return ToolSchema(
      name: _name,
      description: _description,
      parameters: ParametersSchema(
        type: 'object',
        properties: {},
        required: [],
        additionalProperties: false,
      ),
      strict: _strict,
    );
  }

  /// Abstract method for subclasses to implement the actual tool logic.
  Future<ReturnT> run(ArgsT args, CancellationToken cancellationToken);

  @override
  Future<ReturnT> runJson(
    Map<String, dynamic> args,
    CancellationToken cancellationToken, {
    String? callId,
  }) async {
    // In a real implementation, 'args' would be converted to ArgsT.
    // This requires a way to deserialize Map<String, dynamic> to ArgsT.
    // For now, we'll assume ArgsT can be constructed from the map directly or is dynamic.
    final typedArgs = _convertArgs(args); // Placeholder for actual deserialization

    final result = await run(typedArgs, cancellationToken);

    // Log the tool call event
    autogenLogger.info(ToolCallEvent(
      toolName: name,
      arguments: args,
      result: returnValueAsString(result),
    ));

    return result;
  }

  // Placeholder for argument conversion.
  // This would typically involve using json_serializable's fromJson or a custom factory.
  ArgsT _convertArgs(Map<String, dynamic> args) {
    // This is a simplified conversion. In a real scenario, you'd need
    // to know the concrete type of ArgsT and have a fromJson factory for it.
    // For example: if (ArgsT == MyArgs) return MyArgs.fromJson(args) as ArgsT;
    return args as ArgsT; // Unsafe cast, for demonstration
  }

  @override
  Future<Map<String, dynamic>> saveStateJson() async => {};

  @override
  Future<void> loadStateJson(Map<String, dynamic> state) async {}
}

/// Base implementation for a streaming tool.
abstract class BaseStreamTool<ArgsT, StreamT, ReturnT> extends BaseTool<ArgsT, ReturnT>
    implements StreamTool<ArgsT, StreamT, ReturnT> {
  BaseStreamTool({
    required super.name,
    required super.description,
    super.strict,
  });

  @override
  Stream<dynamic> runJsonStream(
    Map<String, dynamic> args,
    CancellationToken cancellationToken, {
    String? callId,
  }) async* {
    final typedArgs = _convertArgs(args); // Placeholder for deserialization
    await for (final result in runStream(typedArgs, cancellationToken)) {
      yield result;
    }
  }

  /// Abstract method for subclasses to implement the actual streaming tool logic.
  Stream<dynamic> runStream(ArgsT args, CancellationToken cancellationToken);
}

/// Base implementation for a tool with state.
abstract class BaseToolWithState<ArgsT, ReturnT, StateT> extends BaseTool<ArgsT, ReturnT>
    implements ToolWithState<ArgsT, ReturnT, StateT> {
  BaseToolWithState({
    required super.name,
    required super.description,
    super.strict,
  });

  @override
  Future<Map<String, dynamic>> saveStateJson() async {
    final state = saveState();
    // This assumes StateT has a toJson method or can be directly converted to Map.
    // If StateT is a Freezed class, it will have toJson.
    if (state is Map<String, dynamic> Function()) {
      return state();
    }
    return {}; // Placeholder
  }

  @override
  Future<void> loadStateJson(Map<String, dynamic> state) async {
    // This assumes StateT has a fromJson factory.
    // For now, it's a placeholder.
    loadState(state as StateT); // Unsafe cast, for demonstration
  }
}
