import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:logging/logging.dart';

import '../cancellation_token.dart';
import '../exceptions.dart';
import 'base.dart';
import 'workbench.dart'; // Assuming workbench.dart defines Workbench, ToolResult, TextResultContent

part 'generated/static_workbench.freezed.dart';
part 'generated/static_workbench.g.dart';

final _staticWorkbenchLogger = Logger('AutoGen.StaticWorkbench');

/// Configuration for a static workbench.
@freezed
class StaticWorkbenchConfig with _$StaticWorkbenchConfig {
  const factory StaticWorkbenchConfig({
    // In Dart, tools are not directly serialized in config.
    // Instead, the workbench would be constructed with pre-instantiated tools.
    // This config might be used for metadata or overrides.
    @Default({}) Map<String, ToolOverride> toolOverrides,
  }) = _StaticWorkbenchConfig;

  factory StaticWorkbenchConfig.fromJson(Map<String, dynamic> json) =>
      _$StaticWorkbenchConfigFromJson(json);
}

/// State for a static workbench.
@freezed
class StaticWorkbenchState with _$StaticWorkbenchState {
  const factory StaticWorkbenchState({
    @Default('StaticWorkbenchState') String type,
    @Default({}) Map<String, Map<String, dynamic>> tools,
  }) = _StaticWorkbenchState;

  factory StaticWorkbenchState.fromJson(Map<String, dynamic> json) =>
      _$StaticWorkbenchStateFromJson(json);
}

/// A workbench that provides a static set of tools that do not change after
/// each tool execution.
class StaticWorkbench implements Workbench {
  final List<Tool<dynamic, dynamic>> _tools;
  final Map<String, ToolOverride> _toolOverrides;
  final Map<String, String> _overrideNameToOriginal;

  /// Creates a [StaticWorkbench].
  ///
  /// - [tools]: A list of tools to be included in the workbench.
  /// - [toolOverrides]: Optional mapping of original tool names to override
  ///   configurations for name and/or description.
  StaticWorkbench({
    required List<Tool<dynamic, dynamic>> tools,
    Map<String, ToolOverride>? toolOverrides,
  })  : _tools = tools,
        _toolOverrides = toolOverrides ?? {},
        _overrideNameToOriginal = {} {
    final existingToolNames = _tools.map((tool) => tool.name).toSet();

    for (final entry in _toolOverrides.entries) {
      final originalName = entry.key;
      final override = entry.value;

      if (override.name != null && override.name != originalName) {
        // Check for conflicts with existing tool names
        if (existingToolNames.contains(override.name) &&
            override.name != originalName) {
          throw ArgumentError(
              'Tool override name \'${override.name}\' conflicts with existing tool name. ' +
              'Override names must not conflict with any tool names.');
        }
        // Check for conflicts with other override names
        if (_overrideNameToOriginal.containsKey(override.name)) {
          final existingOriginal = _overrideNameToOriginal[override.name]!;
          throw ArgumentError(
              'Tool override name \'${override.name}\' is used by multiple tools: ' +
              '\'${existingOriginal}\' and \'${originalName}\'. Override names must be unique.');
        }
        _overrideNameToOriginal[override.name!] = originalName;
      }
    }
  }

  @override
  Future<List<ToolSchema>> listTools() async {
    final resultSchemas = <ToolSchema>[];
    for (final tool in _tools) {
      final originalSchema = tool.schema;
      ToolSchema schema;

      // Apply overrides if they exist for this tool
      if (_toolOverrides.containsKey(tool.name)) {
        final override = _toolOverrides[tool.name]!;
        schema = originalSchema.copyWith(
          name: override.name ?? originalSchema.name,
          description: override.description ?? originalSchema.description,
        );
      } else {
        schema = originalSchema;
      }
      resultSchemas.add(schema);
    }
    return resultSchemas;
  }

  @override
  Future<ToolResult> callTool(
    String name,
    Map<String, dynamic>? arguments,
    CancellationToken? cancellationToken,
    {
    String? callId,
  }) async {
    // Check if the name is an override name and map it back to the original
    final originalName = _overrideNameToOriginal[name] ?? name;

    final tool = _tools.firstWhereOrNull((tool) => tool.name == originalName);
    if (tool == null) {
      return ToolResult(
        name: name,
        content: [TextResultContent(content: 'Tool $name not found.')],
        isError: true,
      );
    }

    final token = cancellationToken ?? CancellationToken();
    final args = arguments ?? {};

    try {
      final actualToolOutput = await tool.runJson(args, token, callId: callId);
      final resultStr = tool.returnValueAsString(actualToolOutput);
      return ToolResult(
        name: name,
        content: [TextResultContent(content: resultStr)],
        isError: false,
      );
    } catch (e, s) {
      _staticWorkbenchLogger.severe('Error calling tool $name', e, s);
      final resultStr = _formatErrors(e);
      return ToolResult(
        name: name,
        content: [TextResultContent(content: resultStr)],
        isError: true,
      );
    }
  }

  @override
  Future<void> start() async {}

  @override
  Future<void> stop() async {}

  @override
  Future<void> reset() async {}

  @override
  Future<Map<String, dynamic>> saveState() async {
    final toolStates = StaticWorkbenchState();
    for (final tool in _tools) {
      toolStates.tools[tool.name] = await tool.saveStateJson();
    }
    return toolStates.toJson();
  }

  @override
  Future<void> loadState(Map<String, dynamic> state) async {
    final parsedState = StaticWorkbenchState.fromJson(state);
    for (final tool in _tools) {
      if (parsedState.tools.containsKey(tool.name)) {
        await tool.loadStateJson(parsedState.tools[tool.name]!);
      }
    }
  }

  String _formatErrors(dynamic error) {
    // Dart does not have Python's ExceptionGroup.
    // We'll just return the string representation of the error.
    return error.toString();
  }
}

/// A workbench that provides a static set of tools and supports streaming results.
class StaticStreamWorkbench extends StaticWorkbench implements StreamWorkbench {
  StaticStreamWorkbench({
    required super.tools,
    super.toolOverrides,
  });

  @override
  Stream<dynamic> callToolStream(
    String name,
    Map<String, dynamic>? arguments,
    CancellationToken? cancellationToken,
    {
    String? callId,
  }) async* {
    final originalName = _overrideNameToOriginal[name] ?? name;
    final tool = _tools.firstWhereOrNull((tool) => tool.name == originalName);

    if (tool == null) {
      yield ToolResult(
        name: name,
        content: [TextResultContent(content: 'Tool $name not found.')],
        isError: true,
      );
      return;
    }

    final token = cancellationToken ?? CancellationToken();
    final args = arguments ?? {};

    try {
      if (tool is StreamTool) {
        await for (final result in tool.runJsonStream(args, token, callId: callId)) {
          yield result;
        }
      } else {
        // If the tool is not a stream tool, run it normally and yield the result
        final actualToolOutput = await tool.runJson(args, token, callId: callId);
        final resultStr = tool.returnValueAsString(actualToolOutput);
        yield ToolResult(
          name: name,
          content: [TextResultContent(content: resultStr)],
          isError: false,
        );
      }
    } catch (e, s) {
      _staticWorkbenchLogger.severe('Error calling streaming tool $name', e, s);
      final resultStr = _formatErrors(e);
      yield ToolResult(
        name: name,
        content: [TextResultContent(content: resultStr)],
        isError: true,
      );
    }
  }
}

extension on List<Tool> {
  Tool? firstWhereOrNull(bool Function(Tool) test) {
    for (final element in this) {
      if (test(element)) {
        return element;
      }
    }
    return null;
  }
}
