import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../cancellation_token.dart';
import '../image.dart'; // Assuming image.dart defines Image

part 'generated/workbench.freezed.dart';
part 'generated/workbench.g.dart';

/// Base class for content within a tool result.
@Freezed(unionKey: 'type')
sealed class ResultContent with _$ResultContent {
  const factory ResultContent.text({
    @Default('TextResultContent') String type,
    required String content,
  }) = TextResultContent;

  const factory ResultContent.image({
    @Default('ImageResultContent') String type,
    required Image content,
  }) = ImageResultContent;

  factory ResultContent.fromJson(Map<String, dynamic> json) =>
      _$ResultContentFromJson(json);
}

/// A result of a tool execution by a workbench.
@freezed
class ToolResult with _$ToolResult {
  const factory ToolResult({
    @Default('ToolResult') String type,
    required String name,
    required List<ResultContent> content,
    @Default(false) bool isError,
  }) = _ToolResult;

  factory ToolResult.fromJson(Map<String, dynamic> json) =>
      _$ToolResultFromJson(json);

  /// Converts the result to a text string.
  ///
  /// - [replaceImage]: The string to replace image content with. If null,
  ///   image content will be included as a base64 string.
  String toText({String? replaceImage}) {
    final parts = <String>[];
    for (final item in content) {
      item.map(
        text: (textContent) => parts.add(textContent.content),
        image: (imageContent) => parts.add(
          replaceImage ?? '[Image: ${imageContent.content.toBase64()}]',
        ),
      );
    }
    return parts.join('\n');
  }
}

/// An abstract class that defines the contract for a workbench.
///
/// A workbench provides a set of tools that may share resources and state.
/// It is responsible for managing the lifecycle of the tools and providing
/// a single interface to call them.
abstract class Workbench {
  /// Lists the currently available tools in the workbench as [ToolSchema] objects.
  Future<List<ToolSchema>> listTools();

  /// Calls a tool in the workbench.
  ///
  /// - [name]: The name of the tool to call.
  /// - [arguments]: The arguments to pass to the tool.
  /// - [cancellationToken]: An optional cancellation token.
  /// - [callId]: An optional identifier for the tool call, used for tracing.
  Future<ToolResult> callTool(
    String name,
    Map<String, dynamic>? arguments,
    CancellationToken? cancellationToken, {
    String? callId,
  });

  /// Starts the workbench and initializes any resources.
  Future<void> start();

  /// Stops the workbench and releases any resources.
  Future<void> stop();

  /// Resets the workbench to its initialized, started state.
  Future<void> reset();

  /// Saves the state of the workbench.
  Future<Map<String, dynamic>> saveState();

  /// Loads the state of the workbench.
  Future<void> loadState(Map<String, dynamic> state);
}

/// An abstract class that defines the contract for a workbench that supports
/// streaming results from tool calls.
abstract class StreamWorkbench implements Workbench {
  /// Calls a tool in the workbench and returns a stream of results.
  ///
  /// - [name]: The name of the tool to call.
  /// - [arguments]: The arguments to pass to the tool.
  /// - [cancellationToken]: An optional cancellation token.
  /// - [callId]: An optional identifier for the tool call, used for tracing.
  Stream<dynamic> callToolStream(
    String name,
    Map<String, dynamic>? arguments,
    CancellationToken? cancellationToken, {
    String? callId,
  });
}
