import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../cancellation_token.dart';
import 'base.dart';

part 'generated/function_tool.freezed.dart';
part 'generated/function_tool.g.dart';

/// Configuration for a function tool.
@freezed
class FunctionToolConfig with _$FunctionToolConfig {
  const factory FunctionToolConfig({
    required String name,
    required String description,
    // In Dart, we cannot represent 'source_code' or 'global_imports'
    // for dynamic execution as in Python.
    // Tools must be defined with pre-compiled Dart functions.
    @Default(false) bool hasCancellationSupport,
  }) = _FunctionToolConfig;

  factory FunctionToolConfig.fromJson(Map<String, dynamic> json) =>
      _$FunctionToolConfigFromJson(json);
}

/// A tool that wraps a standard Dart function.
///
/// [FunctionTool] allows you to expose a Dart function as a tool that agents
/// can call. It requires explicit type arguments for its parameters and return type.
///
/// - [ArgsT]: The type of the arguments object for the function. This should be
///   a class that can be deserialized from a `Map<String, dynamic>`.
/// - [ReturnT]: The return type of the function.
class FunctionTool<ArgsT, ReturnT> extends BaseTool<ArgsT, ReturnT> {
  final FutureOr<ReturnT> Function(ArgsT args, CancellationToken? cancellationToken) _func;
  final bool _hasCancellationSupport;

  /// Creates a [FunctionTool].
  ///
  /// - [func]: The Dart function to wrap. It must accept an [ArgsT] object
  ///   and an optional [CancellationToken].
  /// - [description]: A description of the function's purpose for the model.
  /// - [name]: An optional custom name for the tool. Defaults to a generated name.
  /// - [hasCancellationSupport]: Whether the wrapped function supports cancellation.
  /// - [strict]: If true, the tool schema will enforce all arguments as required.
  FunctionTool(
    this._func,
    {
    required String description,
    String? name,
    bool hasCancellationSupport = false,
    bool strict = false,
  })  : _hasCancellationSupport = hasCancellationSupport,
        super(
          name: name ?? _generateFunctionName(func),
          description: description,
          strict: strict,
        );

  // A simple way to generate a function name if not provided.
  // In a real scenario, you might use a more robust approach or require explicit naming.
  static String _generateFunctionName(Function func) {
    // This is a placeholder. Dart doesn't easily expose function names at runtime.
    // For production, explicit naming is recommended.
    return 'function_${func.hashCode}';
  }

  @override
  Future<ReturnT> run(ArgsT args, CancellationToken cancellationToken) async {
    if (_hasCancellationSupport) {
      return await _func(args, cancellationToken);
    } else {
      return await _func(args, null); // Pass null if not supported
    }
  }

  // In Dart, we cannot dynamically load functions from source code.
  // Therefore, _from_config and _to_config methods for dynamic code
  // execution are not applicable here.
  // Tools must be instantiated with pre-compiled Dart functions.
}
