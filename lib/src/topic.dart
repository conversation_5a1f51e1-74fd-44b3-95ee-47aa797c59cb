import 'package:freezed_annotation/freezed_annotation.dart';

part 'generated/topic.freezed.dart';

final _validTopicTypeRegex = RegExp(r'^[
\w\-\.\:\=]+\Z');

/// A data class that defines the scope of a broadcast message.
///
/// It follows a publish-subscribe model, where a message is published to a specific topic.
@freezed
class TopicId with _$TopicId {
  /// Factory constructor for [TopicId].
  const factory TopicId({
    /// The type of the event that this topic contains, adhering to the CloudEvents spec.
    /// Must match the pattern: `^[
\w\-\.\:\=]+\Z`
    required String type,

    /// Identifies the context in which an event happened, adhering to the CloudEvents spec.
    required String source,
  }) = _TopicId;

  /// Private constructor for [freezed].
  const TopicId._();

  /// Validates the [type] field upon construction.
  @Assert('TopicId.isValidTopicType(type)', 'Invalid topic type. Must match regex: r'^[\w\-\.\:\=]+\Z'')
  factory TopicId.validated({
    required String type,
    required String source,
  }) {
    if (!isValidTopicType(type)) {
      throw ArgumentError.value(type, 'type', 'Invalid topic type. Must match regex: r'^[\w\-\.\:\=]+\Z'');
    }
    return TopicId(type: type, source: source);
  }

  /// Creates a [TopicId] from a string of the format "type/source".
  factory TopicId.fromStr(String topicId) {
    final parts = topicId.split('/');
    if (parts.length != 2) {
      throw ArgumentError.value(topicId, 'topicId', 'Invalid topic id format. Expected "type/source".');
    }
    return TopicId.validated(type: parts[0], source: parts[1]);
  }

  /// Checks if the given value is a valid topic type.
  static bool isValidTopicType(String value) {
    return _validTopicTypeRegex.hasMatch(value);
  }

  @override
  String toString() => '$type/$source';
}
