import 'dart:async';

/// A token used to signal cancellation of asynchronous operations.
class CancellationToken {
  bool _isCancelled = false;
  final _callbacks = <void Function()>[];

  /// Whether this token has been cancelled.
  bool get isCancelled => _isCancelled;

  /// Cancels the token and invokes all registered callbacks.
  ///
  /// If the token is already cancelled, this method does nothing.
  void cancel() {
    if (_isCancelled) return;

    _isCancelled = true;
    for (final callback in _callbacks) {
      try {
        callback();
      } catch (e) {
        // Log the error, but don't let it stop other callbacks.
        print('Error in cancellation callback: $e');
      }
    }
    _callbacks.clear();
  }

  /// Adds a callback to be invoked when the token is cancelled.
  ///
  /// If the token is already cancelled, the callback is invoked immediately.
  void addCallback(void Function() callback) {
    if (_isCancelled) {
      callback();
    } else {
      _callbacks.add(callback);
    }
  }
}
