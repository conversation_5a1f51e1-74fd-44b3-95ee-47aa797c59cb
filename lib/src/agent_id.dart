import 'package:freezed_annotation/freezed_annotation.dart';

part 'generated/agent_id.freezed.dart';

final _validAgentTypeRegex = RegExp(r'^[\w\-\.]+\Z');

/// A data class that uniquely identifies an agent instance within an agent runtime.
///
/// It serves as the 'address' of the agent instance for receiving messages.
/// It consists of a [type] and a [key].
@freezed
class AgentId with _$AgentId {
  /// Factory constructor for [AgentId].
  ///
  /// Throws a [ValueError] if the [type] is invalid.
  const factory AgentId({
    /// An identifier that associates an agent with a specific factory function.
    /// Must only be composed of alphanumeric letters (a-z, 0-9), hyphens (-), or periods (.).
    required String type,

    /// The unique instance identifier for an agent of a given type.
    required String key,
  }) = _AgentId;

  /// Private constructor for [freezed].
  const AgentId._();

  /// Validates the [type] field upon construction.
  @Assert('AgentId.isValidAgentType(type)', 'Invalid agent type. Must match regex: r'^[\\w\\-\\.]+\\Z'')
  factory AgentId.validated({
    required String type,
    required String key,
  }) {
    if (!isValidAgentType(type)) {
      throw ArgumentError.value(type, 'type', 'Invalid agent type. Must match regex: r'^[\\w\\-\\.]+\\Z'');
    }
    return AgentId(type: type, key: key);
  }

  /// Creates an [AgentId] from a string of the format "type/key".
  factory AgentId.fromStr(String agentId) {
    final parts = agentId.split('/');
    if (parts.length != 2) {
      throw ArgumentError.value(agentId, 'agentId', 'Invalid agent id format. Expected "type/key".');
    }
    return AgentId.validated(type: parts[0], key: parts[1]);
  }

  /// Checks if the given value is a valid agent type.
  static bool isValidAgentType(String value) {
    return _validAgentTypeRegex.hasMatch(value);
  }

  @override
  String toString() => '$type/$key';
}
