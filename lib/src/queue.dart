import 'dart:async';
import 'dart:collection';

/// Exception raised when putting onto or getting from a shut-down Queue.
class QueueShutDown implements Exception {
  final String message;
  QueueShutDown([this.message = 'Queue has been shut down.']);

  @override
  String toString() => 'QueueShutDown: $message';
}

/// A simple asynchronous queue implementation for Dart.
///
/// This queue supports `put`, `get`, `taskDone`, and `join` operations,
/// similar to Python's `asyncio.Queue`.
class Queue<T> {
  final int _maxsize;
  final _queue = DoubleLinkedQueue<T>();
  final _getters = DoubleLinkedQueue<Completer<void>>();
  final _putters = DoubleLinkedQueue<Completer<void>>();
  int _unfinishedTasks = 0;
  final _finished = Completer<void>();
  bool _isShutdown = false;

  Queue([this._maxsize = 0]) {
    _finished.complete(); // Initially, no unfinished tasks
  }

  /// The number of items currently in the queue.
  int get qsize => _queue.length;

  /// The maximum number of items allowed in the queue.
  int get maxsize => _maxsize;

  /// Returns true if the queue is empty.
  bool get isEmpty => _queue.isEmpty;

  /// Returns true if the queue is full.
  bool get isFull => _maxsize > 0 && qsize >= _maxsize;

  /// Puts an item into the queue.
  ///
  /// If the queue is full, waits until a free slot is available.
  /// Throws [QueueShutDown] if the queue has been shut down.
  Future<void> put(T item) async {
    while (isFull) {
      if (_isShutdown) {
        throw QueueShutDown();
      }
      final putter = Completer<void>();
      _putters.add(putter);
      try {
        await putter.future;
      } on Exception {
        _putters.remove(putter);
        if (!isFull && !putter.isCompleted) {
          _wakeupNext(_putters);
        }
        rethrow;
      }
    }
    putNowait(item);
  }

  /// Puts an item into the queue without blocking.
  ///
  /// Throws [StateError] if the queue is full.
  /// Throws [QueueShutDown] if the queue has been shut down.
  void putNowait(T item) {
    if (_isShutdown) {
      throw QueueShutDown();
    }
    if (isFull) {
      throw StateError('Queue is full');
    }
    _queue.add(item);
    _unfinishedTasks++;
    if (_finished.isCompleted) {
      _finished = Completer<void>(); // Reset if previously completed
    }
    _wakeupNext(_getters);
  }

  /// Removes and returns an item from the queue.
  ///
  /// If the queue is empty, waits until an item is available.
  /// Throws [QueueShutDown] if the queue has been shut down and is empty.
  Future<T> get() async {
    while (isEmpty) {
      if (_isShutdown && isEmpty) {
        throw QueueShutDown();
      }
      final getter = Completer<void>();
      _getters.add(getter);
      try {
        await getter.future;
      } on Exception {
        _getters.remove(getter);
        if (!isEmpty && !getter.isCompleted) {
          _wakeupNext(_getters);
        }
        rethrow;
      }
    }
    return getNowait();
  }

  /// Removes and returns an item from the queue without blocking.
  ///
  /// Throws [StateError] if the queue is empty.
  /// Throws [QueueShutDown] if the queue has been shut down and is empty.
  T getNowait() {
    if (isEmpty) {
      if (_isShutdown) {
        throw QueueShutDown();
      }
      throw StateError('Queue is empty');
    }
    final item = _queue.removeFirst();
    _wakeupNext(_putters);
    return item;
  }

  /// Indicates that a formerly enqueued task is complete.
  ///
  /// Throws [StateError] if called more times than there were items placed in the queue.
  void taskDone() {
    if (_unfinishedTasks <= 0) {
      throw StateError('taskDone() called too many times');
    }
    _unfinishedTasks--;
    if (_unfinishedTasks == 0) {
      _finished.complete();
    }
  }

  /// Blocks until all items in the queue have been gotten and processed.
  Future<void> join() async {
    if (_unfinishedTasks > 0) {
      await _finished.future;
    }
  }

  /// Shuts down the queue, making queue gets and puts raise [QueueShutDown].
  ///
  /// - [immediate]: If true, gets will raise immediately. Otherwise, gets will
  ///   only raise once the queue is empty.
  void shutdown({bool immediate = false}) {
    _isShutdown = true;
    if (immediate) {
      while (_queue.isNotEmpty) {
        _queue.removeFirst();
        if (_unfinishedTasks > 0) {
          _unfinishedTasks--;
        }
      }
      if (_unfinishedTasks == 0 && !_finished.isCompleted) {
        _finished.complete();
      }
    }
    // Wake up all getters and putters
    while (_getters.isNotEmpty) {
      final getter = _getters.removeFirst();
      if (!getter.isCompleted) {
        getter.complete();
      }
    }
    while (_putters.isNotEmpty) {
      final putter = _putters.removeFirst();
      if (!putter.isCompleted) {
        putter.complete();
      }
    }
  }

  void _wakeupNext(DoubleLinkedQueue<Completer<void>> waiters) {
    while (waiters.isNotEmpty) {
      final waiter = waiters.removeFirst();
      if (!waiter.isCompleted) {
        waiter.complete();
        break;
      }
    }
  }
}
