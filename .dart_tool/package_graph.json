{"roots": ["autogen_core"], "packages": [{"name": "autogen_core", "version": "0.0.1", "dependencies": ["freezed_annotation", "json_annotation", "logging"], "devDependencies": ["build_runner", "freezed", "json_serializable", "lints"]}, {"name": "lints", "version": "3.0.0", "dependencies": []}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "json_serializable", "version": "6.9.5", "dependencies": ["analyzer", "async", "build", "build_config", "collection", "dart_style", "json_annotation", "meta", "path", "pub_semver", "pubspec_parse", "source_gen", "source_helper"]}, {"name": "source_gen", "version": "2.0.0", "dependencies": ["analyzer", "async", "build", "dart_style", "glob", "path", "pub_semver", "source_span", "yaml"]}, {"name": "source_helper", "version": "1.3.5", "dependencies": ["analyzer", "collection", "source_gen"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "build_config", "version": "1.1.2", "dependencies": ["checked_yaml", "json_annotation", "path", "pubspec_parse", "yaml"]}, {"name": "meta", "version": "1.17.0", "dependencies": []}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "dart_style", "version": "3.1.0", "dependencies": ["analyzer", "args", "collection", "package_config", "path", "pub_semver", "source_span", "yaml"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "build", "version": "2.5.4", "dependencies": ["analyzer", "async", "build_runner_core", "built_collection", "built_value", "convert", "crypto", "glob", "graphs", "logging", "meta", "package_config", "path", "pool"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "build_runner_core", "version": "9.1.2", "dependencies": ["analyzer", "async", "build", "build_config", "build_resolvers", "build_runner", "built_collection", "built_value", "collection", "convert", "crypto", "glob", "graphs", "json_annotation", "logging", "meta", "package_config", "path", "pool", "timing", "watcher", "yaml"]}, {"name": "build_runner", "version": "2.5.4", "dependencies": ["analyzer", "args", "async", "build", "build_config", "build_daemon", "build_resolvers", "build_runner_core", "code_builder", "collection", "crypto", "dart_style", "frontend_server_client", "glob", "graphs", "http", "http_multi_server", "io", "js", "logging", "meta", "mime", "package_config", "path", "pool", "pub_semver", "pubspec_parse", "shelf", "shelf_web_socket", "stack_trace", "stream_transform", "timing", "watcher", "web", "web_socket_channel", "yaml"]}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "build_resolvers", "version": "2.5.4", "dependencies": ["analyzer", "async", "build", "build_runner_core", "collection", "convert", "crypto", "graphs", "logging", "package_config", "path", "pool", "pub_semver", "stream_transform"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "timing", "version": "1.0.2", "dependencies": ["json_annotation"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "build_daemon", "version": "4.0.4", "dependencies": ["built_collection", "built_value", "crypto", "http_multi_server", "logging", "path", "pool", "shelf", "shelf_web_socket", "stream_transform", "watcher", "web_socket_channel"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "watcher", "version": "1.1.2", "dependencies": ["async", "path"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "shelf_web_socket", "version": "3.0.0", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "web_socket_channel", "version": "3.0.3", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "web_socket", "version": "1.0.1", "dependencies": ["web"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "test_api", "version": "0.7.6", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "freezed", "version": "2.5.8", "dependencies": ["analyzer", "build", "build_config", "collection", "dart_style", "freezed_annotation", "json_annotation", "meta", "source_gen"]}, {"name": "analyzer", "version": "7.5.4", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "85.0.0", "dependencies": ["meta"]}, {"name": "args", "version": "2.7.0", "dependencies": []}], "configVersion": 1}