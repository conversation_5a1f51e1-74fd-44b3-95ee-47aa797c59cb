import 'dart:async';

import 'package:analyzer/dart/element/element.dart';
import 'package:analyzer/dart/element/type.dart';
import 'package:build/build.dart';
import 'package:code_builder/code_builder.dart';
import 'package:dart_style/dart_style.dart';
import 'package:source_gen/source_gen.dart';

import 'package:autogen_core/src/annotations.dart'; // Import our annotations
import 'package:autogen_core/src/message_context.dart'; // Import MessageContext for type checking

/// Creates a [Builder] that generates routing mixins for [RoutedAgent] classes.
Builder routedAgentBuilder(BuilderOptions options) =>
    SharedPartBuilder([_RoutedAgentGenerator()], 'autogen_core');

/// A [Generator] that processes classes annotated with [RoutedAgentConfig].
class _RoutedAgentGenerator extends GeneratorForAnnotation<RoutedAgentConfig> {
  @override
  FutureOr<String> generateForAnnotatedElement(
    Element element,
    ConstantReader annotation,
    BuildStep buildStep,
  ) async {
    if (element is! ClassElement) {
      throw InvalidGenerationSourceError(
        '${element.displayName} is not a class and cannot be annotated with @RoutedAgentConfig.',
        element: element,
      );
    }

    final classElement = element;
    final className = classElement.displayName;
    final mixinName = '_\$${className}Router'; // e.g., _\$MyAgentRouter

    // Check if the class extends RoutedAgent
    if (!classElement.allSupertypes.any((s) => s.element?.name == 'RoutedAgent')) {
      throw InvalidGenerationSourceError(
        'Class $className must extend RoutedAgent to use @RoutedAgentConfig.',
        element: element,
      );
    }

    // Collect message handlers
    final handlers = <_MessageHandlerInfo>[];
    for (final method in classElement.methods) {
      final rpcAnnotation = const TypeChecker.fromRuntime(Rpc).firstAnnotationOfExact(method);
      final eventAnnotation = const TypeChecker.fromRuntime(Event).firstAnnotationOfExact(method);

      if (rpcAnnotation != null || eventAnnotation != null) {
        final isRpc = rpcAnnotation != null;
        final handlerAnnotation = rpcAnnotation ?? eventAnnotation!;
        final matchExpression = handlerAnnotation.getField('match')?.toFunctionValue();

        // Validate method signature: (dynamic message, MessageContext ctx)
        if (method.parameters.length != 2 ||
            !method.parameters[0].type.isDartCoreObject || // message should be dynamic or Object
            method.parameters[1].type.element?.name != 'MessageContext') { // ctx
          throw InvalidGenerationSourceError(
            'Method ${method.displayName} in $className must have signature (dynamic message, MessageContext ctx).',
            element: method,
          );
        }

        final messageType = method.parameters[0].type;
        final returnType = method.returnType;

        handlers.add(_MessageHandlerInfo(
          methodName: method.displayName,
          messageType: messageType,
          returnType: returnType,
          isRpc: isRpc,
          matchExpression: matchExpression,
          // Determine the correct way to call the match function
          matchFunctionName: matchExpression != null
              ? (matchExpression.isStatic && matchExpression.enclosingElement == classElement
                  ? '${className}.${matchExpression.name}' // Static method of the current class
                  : matchExpression.name) // Top-level function or static method of another class
              : null,
        ));
      }
    }

    // Sort handlers by message type name and then method name for deterministic code generation
    // This ensures that if multiple handlers handle the same message type, their order is consistent.
    // The Python version mentions alphabetical order for match functions, so we'll try to mimic that.
    handlers.sort((a, b) {
      final typeComparison = a.messageType.getDisplayString(with: DisplayStringKind.fullyQualified).compareTo(b.messageType.getDisplayString(with: DisplayStringKind.fullyQualified));
      if (typeComparison != 0) return typeComparison;
      return a.methodName.compareTo(b.methodName);
    });

    // Build the mixin
    final mixinBuilder = MixinBuilder()
      ..name = mixinName
      ..on = Reference(className) // This mixin is intended for the specific class
      ..methods.add(Method((m) => m
        ..name = 'routeMessage'
        ..returns = refer('Future<dynamic>')
        ..requiredParameters.addAll([
          Parameter((p) => p
            ..name = 'message'
            ..type = refer('dynamic')),
          Parameter((p) => p
            ..name = 'ctx'
            ..type = refer('MessageContext')),
        ])
        ..modifier = MethodModifier.async
        ..body = Block.fromParts([
          Code('switch (message.runtimeType) {'),
          for (final handler in handlers)
            Code('''
              case ${handler.messageType.getDisplayString(with: DisplayStringKind.fullyQualified)}:
                // Cast message to the expected type for type safety within the handler
                final _typedMessage = message as ${handler.messageType.getDisplayString(with: DisplayStringKind.fullyQualified)};
                // Check RPC status if it's an event handler
                ${handler.isRpc ? '' : 'if (!ctx.isRpc) {'}
                  ${handler.matchFunctionName != null ? 'if (${handler.matchFunctionName}(_typedMessage, ctx)) {' : ''}
                    return (this as ${className}).${handler.methodName}(_typedMessage, ctx);
                  ${handler.matchFunctionName != null ? '}' : ''}
                ${handler.isRpc ? '' : '}'}
                break;
            '''),
          Code('''
            default:
              return (this as ${className}).onUnhandledMessage(message, ctx);
          '''),
          Code('}'), // End of switch
        ]),
      ));

    final emitter = DartEmitter(allocator: Allocator.simplePrefixing());
    final generatedCode = DartFormatter().format('${mixinBuilder.build().accept(emitter)}');

    return generatedCode;
  }
}

/// Internal class to hold information about a message handler method.
class _MessageHandlerInfo {
  final String methodName;
  final DartType messageType;
  final DartType returnType;
  final bool isRpc;
  final FunctionElement? matchExpression; // The actual FunctionElement
  final String? matchFunctionName; // The string to use in generated code (e.g., 'MyClass.myMatch')

  _MessageHandlerInfo({
    required this.methodName,
    required this.messageType,
    required this.returnType,
    required this.isRpc,
    this.matchExpression,
    this.matchFunctionName,
  });
}
